"""
OpenAI LLM Provider Implementation
"""

import asyncio
import json
from typing import Dict, List, Optional, Any
import aiohttp
from ..utils.logging_config import get_logger
from .llm_provider import LLMProvider, LLMResponse, LLMConfig, LLMProviderType

logger = get_logger("openai_provider")

class OpenAIProvider(LLMProvider):
    """OpenAI API provider implementation"""
    
    # Model pricing per 1K tokens (input, output)
    MODEL_PRICING = {
        "gpt-4": (0.03, 0.06),
        "gpt-4-turbo": (0.01, 0.03),
        "gpt-4o": (0.005, 0.015),
        "gpt-4o-mini": (0.00015, 0.0006),
        "gpt-3.5-turbo": (0.0015, 0.002),
        "gpt-3.5-turbo-16k": (0.003, 0.004)
    }
    
    def __init__(self, config: LLMConfig):
        super().__init__(config)
        self.base_url = config.base_url or "https://api.openai.com/v1"
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def initialize(self) -> bool:
        """Initialize OpenAI provider"""
        try:
            if not self.validate_config():
                return False
            
            # Create HTTP session
            timeout = aiohttp.ClientTimeout(total=self.config.timeout)
            self.session = aiohttp.ClientSession(timeout=timeout)
            
            # Test API connection
            await self._test_connection()
            
            self.is_initialized = True
            logger.info(f"OpenAI provider initialized with model: {self.config.model}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI provider: {e}")
            return False
    
    async def _test_connection(self):
        """Test API connection"""
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        async with self.session.get(
            f"{self.base_url}/models",
            headers=headers
        ) as response:
            if response.status != 200:
                raise Exception(f"API connection test failed: {response.status}")
    
    async def generate_response(
        self, 
        messages: List[Dict[str, str]], 
        system_prompt: Optional[str] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate response from OpenAI"""
        try:
            if not self.is_initialized:
                return LLMResponse(
                    content="",
                    success=False,
                    error="Provider not initialized"
                )
            
            # Prepare messages
            if system_prompt and messages and messages[0].get("role") != "system":
                messages.insert(0, {"role": "system", "content": system_prompt})
            
            # Prepare request
            request_data = {
                "model": self.config.model,
                "messages": messages,
                "temperature": kwargs.get("temperature", self.config.temperature),
                "max_tokens": kwargs.get("max_tokens", self.config.max_tokens),
                **self.config.extra_params
            }
            
            headers = {
                "Authorization": f"Bearer {self.config.api_key}",
                "Content-Type": "application/json"
            }
            
            # Make API call
            async with self.session.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=request_data
            ) as response:
                
                if response.status != 200:
                    error_text = await response.text()
                    return LLMResponse(
                        content="",
                        success=False,
                        error=f"API error {response.status}: {error_text}"
                    )
                
                result = await response.json()
                
                return LLMResponse(
                    content=result["choices"][0]["message"]["content"],
                    success=True,
                    usage=result.get("usage"),
                    model=result.get("model"),
                    provider="openai"
                )
                
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            return LLMResponse(
                content="",
                success=False,
                error=str(e)
            )
    
    async def generate_structured_response(
        self,
        messages: List[Dict[str, str]],
        system_prompt: Optional[str] = None,
        response_format: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> LLMResponse:
        """Generate structured JSON response"""
        try:
            # Add JSON format instruction to system prompt
            json_instruction = "\n\nIMPORTANT: Respond ONLY with valid JSON format. No additional text or explanation."
            
            if response_format:
                json_instruction += f"\n\nRequired JSON structure:\n{json.dumps(response_format, indent=2)}"
            
            if system_prompt:
                system_prompt += json_instruction
            else:
                system_prompt = "You are a helpful assistant that responds in JSON format." + json_instruction
            
            # For models that support response_format parameter
            extra_params = kwargs.copy()
            if self.config.model in ["gpt-4o", "gpt-4o-mini", "gpt-4-turbo"]:
                extra_params["response_format"] = {"type": "json_object"}
            
            response = await self.generate_response(
                messages=messages,
                system_prompt=system_prompt,
                **extra_params
            )
            
            if response.success:
                # Validate JSON response
                parsed_json = self.extract_json_from_response(response.content)
                if parsed_json is None:
                    return LLMResponse(
                        content=response.content,
                        success=False,
                        error="Response is not valid JSON",
                        usage=response.usage,
                        model=response.model,
                        provider=response.provider
                    )
            
            return response
            
        except Exception as e:
            logger.error(f"Structured response generation failed: {e}")
            return LLMResponse(
                content="",
                success=False,
                error=str(e)
            )
    
    def validate_config(self) -> bool:
        """Validate OpenAI configuration"""
        if not self.config.api_key:
            logger.error("OpenAI API key is required")
            return False
        
        if not self.config.model:
            logger.error("OpenAI model is required")
            return False
        
        return True
    
    def get_available_models(self) -> List[str]:
        """Get available OpenAI models"""
        return [
            "gpt-4o",
            "gpt-4o-mini", 
            "gpt-4-turbo",
            "gpt-4",
            "gpt-3.5-turbo",
            "gpt-3.5-turbo-16k"
        ]
    
    def estimate_cost(self, input_tokens: int, output_tokens: int) -> float:
        """Estimate cost for OpenAI API usage"""
        if self.config.model not in self.MODEL_PRICING:
            return 0.0
        
        input_price, output_price = self.MODEL_PRICING[self.config.model]
        
        input_cost = (input_tokens / 1000) * input_price
        output_cost = (output_tokens / 1000) * output_price
        
        return input_cost + output_cost
    
    def cleanup(self):
        """Cleanup OpenAI provider"""
        if self.session:
            asyncio.create_task(self.session.close())
            self.session = None
        
        super().cleanup()
        logger.info("OpenAI provider cleaned up")
