"""
MCP Operators - Blender operators for MCP server and tool management
"""

import bpy
import json
import asyncio
from bpy.types import Operator
from bpy.props import StringProperty, EnumProperty

from ..mcp_integration.server_manager import get_server_manager
from ..mcp_integration.mcp_client import get_mcp_client, MCPServerConfig, TransportType
from ..mcp_integration.tool_registry import get_tool_registry
from ..mcp_integration.context_provider import get_context_provider
from ..utils.logging_config import get_logger

logger = get_logger("mcp_operators")

class AI_AGENT_OT_add_mcp_server_from_template(Operator):
    """Add MCP server from template"""
    bl_idname = "ai_agent.add_mcp_server_from_template"
    bl_label = "Add MCP Server from Template"
    bl_description = "Add a new MCP server from a predefined template"
    bl_options = {'REGISTER'}
    
    template_name: StringProperty(
        name="Template Name",
        description="Name of the template to use"
    )
    
    server_name: StringProperty(
        name="Server Name",
        description="Name for the new server",
        default=""
    )
    
    def invoke(self, context, event):
        # Set default server name based on template
        if not self.server_name and self.template_name:
            server_manager = get_server_manager()
            template = server_manager.get_template(self.template_name)
            if template:
                self.server_name = f"{template.name}_{len(server_manager.list_servers()) + 1}"
        
        return context.window_manager.invoke_props_dialog(self)
    
    def execute(self, context):
        try:
            server_manager = get_server_manager()
            
            # Create server from template
            server_config = server_manager.create_server_from_template(
                self.template_name, 
                self.server_name
            )
            
            if not server_config:
                self.report({'ERROR'}, f"Failed to create server from template: {self.template_name}")
                return {'CANCELLED'}
            
            # Add server
            success = server_manager.add_server(server_config)
            if success:
                self.report({'INFO'}, f"Added MCP server: {self.server_name}")
                logger.info(f"Added MCP server from template {self.template_name}: {self.server_name}")
            else:
                self.report({'ERROR'}, f"Failed to add server: {self.server_name}")
                return {'CANCELLED'}
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to add MCP server: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_connect_mcp_server(Operator):
    """Connect to MCP server"""
    bl_idname = "ai_agent.connect_mcp_server"
    bl_label = "Connect MCP Server"
    bl_description = "Connect to an MCP server"
    bl_options = {'REGISTER'}
    
    server_name: StringProperty(
        name="Server Name",
        description="Name of the server to connect to"
    )
    
    def execute(self, context):
        try:
            server_manager = get_server_manager()
            
            # Use async wrapper for connection
            def connect_callback():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    success = loop.run_until_complete(server_manager.connect_server(self.server_name))
                    loop.close()
                    
                    if success:
                        self.report({'INFO'}, f"Connected to MCP server: {self.server_name}")
                        logger.info(f"Connected to MCP server: {self.server_name}")
                    else:
                        self.report({'ERROR'}, f"Failed to connect to server: {self.server_name}")
                        
                except Exception as e:
                    error_msg = f"Connection error: {e}"
                    self.report({'ERROR'}, error_msg)
                    logger.error(error_msg)
            
            # Run connection in background
            import threading
            thread = threading.Thread(target=connect_callback)
            thread.daemon = True
            thread.start()
            
            self.report({'INFO'}, f"Connecting to {self.server_name}...")
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to connect to MCP server: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_disconnect_mcp_server(Operator):
    """Disconnect from MCP server"""
    bl_idname = "ai_agent.disconnect_mcp_server"
    bl_label = "Disconnect MCP Server"
    bl_description = "Disconnect from an MCP server"
    bl_options = {'REGISTER'}
    
    server_name: StringProperty(
        name="Server Name",
        description="Name of the server to disconnect from"
    )
    
    def execute(self, context):
        try:
            server_manager = get_server_manager()
            
            # Use async wrapper for disconnection
            def disconnect_callback():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    success = loop.run_until_complete(server_manager.disconnect_server(self.server_name))
                    loop.close()
                    
                    if success:
                        self.report({'INFO'}, f"Disconnected from MCP server: {self.server_name}")
                        logger.info(f"Disconnected from MCP server: {self.server_name}")
                    else:
                        self.report({'WARNING'}, f"Server was not connected: {self.server_name}")
                        
                except Exception as e:
                    error_msg = f"Disconnection error: {e}"
                    self.report({'ERROR'}, error_msg)
                    logger.error(error_msg)
            
            # Run disconnection in background
            import threading
            thread = threading.Thread(target=disconnect_callback)
            thread.daemon = True
            thread.start()
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to disconnect from MCP server: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_connect_all_mcp_servers(Operator):
    """Connect to all configured MCP servers"""
    bl_idname = "ai_agent.connect_all_mcp_servers"
    bl_label = "Connect All MCP Servers"
    bl_description = "Connect to all configured MCP servers"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            server_manager = get_server_manager()
            
            def connect_all_callback():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    results = loop.run_until_complete(server_manager.connect_all_servers())
                    loop.close()
                    
                    successful = sum(1 for success in results.values() if success)
                    total = len(results)
                    
                    self.report({'INFO'}, f"Connected to {successful}/{total} MCP servers")
                    logger.info(f"Connected to {successful}/{total} MCP servers")
                    
                except Exception as e:
                    error_msg = f"Error connecting to servers: {e}"
                    self.report({'ERROR'}, error_msg)
                    logger.error(error_msg)
            
            # Run connection in background
            import threading
            thread = threading.Thread(target=connect_all_callback)
            thread.daemon = True
            thread.start()
            
            self.report({'INFO'}, "Connecting to all MCP servers...")
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to connect to MCP servers: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_execute_mcp_tool(Operator):
    """Execute MCP tool"""
    bl_idname = "ai_agent.execute_mcp_tool"
    bl_label = "Execute MCP Tool"
    bl_description = "Execute an MCP tool with arguments"
    bl_options = {'REGISTER'}
    
    tool_name: StringProperty(
        name="Tool Name",
        description="Name of the tool to execute"
    )
    
    arguments_json: StringProperty(
        name="Arguments (JSON)",
        description="Tool arguments in JSON format",
        default="{}"
    )
    
    def invoke(self, context, event):
        # Get tool info for better defaults
        tool_registry = get_tool_registry()
        tool_info = tool_registry.get_tool_info(self.tool_name)
        
        if tool_info and not self.arguments_json.strip() or self.arguments_json == "{}":
            # Provide example arguments based on schema
            schema = tool_info.parameters_schema
            if schema and 'properties' in schema:
                example_args = {}
                for prop_name, prop_info in schema['properties'].items():
                    if prop_info.get('type') == 'string':
                        if 'enum' in prop_info:
                            example_args[prop_name] = prop_info['enum'][0]
                        else:
                            example_args[prop_name] = "example_value"
                    elif prop_info.get('type') == 'number':
                        example_args[prop_name] = 1.0
                    elif prop_info.get('type') == 'integer':
                        example_args[prop_name] = 1
                    elif prop_info.get('type') == 'array':
                        example_args[prop_name] = []
                
                if example_args:
                    self.arguments_json = json.dumps(example_args, indent=2)
        
        return context.window_manager.invoke_props_dialog(self, width=400)
    
    def execute(self, context):
        try:
            tool_registry = get_tool_registry()
            
            # Parse arguments
            try:
                arguments = json.loads(self.arguments_json)
            except json.JSONDecodeError as e:
                self.report({'ERROR'}, f"Invalid JSON arguments: {e}")
                return {'CANCELLED'}
            
            def execute_callback():
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    result = loop.run_until_complete(tool_registry.execute_tool(self.tool_name, arguments))
                    loop.close()
                    
                    if result.success:
                        self.report({'INFO'}, f"Tool executed successfully: {self.tool_name}")
                        logger.info(f"Tool executed: {self.tool_name} - {result.result}")
                    else:
                        self.report({'ERROR'}, f"Tool execution failed: {result.error}")
                        logger.error(f"Tool execution failed: {self.tool_name} - {result.error}")
                        
                except Exception as e:
                    error_msg = f"Tool execution error: {e}"
                    self.report({'ERROR'}, error_msg)
                    logger.error(error_msg)
            
            # Run execution in background
            import threading
            thread = threading.Thread(target=execute_callback)
            thread.daemon = True
            thread.start()
            
            self.report({'INFO'}, f"Executing tool: {self.tool_name}")
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to execute MCP tool: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_refresh_mcp_servers(Operator):
    """Refresh MCP server status"""
    bl_idname = "ai_agent.refresh_mcp_servers"
    bl_label = "Refresh MCP Servers"
    bl_description = "Refresh the status of all MCP servers"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            # Force refresh of UI
            for area in context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
            
            self.report({'INFO'}, "Refreshed MCP server status")
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to refresh MCP servers: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_show_mcp_server_info(Operator):
    """Show MCP server information"""
    bl_idname = "ai_agent.show_mcp_server_info"
    bl_label = "Show MCP Server Info"
    bl_description = "Show detailed information about an MCP server"
    bl_options = {'REGISTER'}
    
    server_name: StringProperty(
        name="Server Name",
        description="Name of the server to show info for"
    )
    
    def execute(self, context):
        try:
            mcp_client = get_mcp_client()
            server_info = mcp_client.get_server_info(self.server_name)
            
            if server_info:
                info_lines = [
                    f"Server: {server_info['name']}",
                    f"Status: {server_info['status']}",
                    f"Transport: {server_info['transport']}",
                ]
                
                if server_info['connected_at']:
                    import time
                    connected_time = time.strftime('%H:%M:%S', time.localtime(server_info['connected_at']))
                    info_lines.append(f"Connected at: {connected_time}")
                
                if server_info['last_error']:
                    info_lines.append(f"Last error: {server_info['last_error']}")
                
                info_message = " | ".join(info_lines)
                self.report({'INFO'}, info_message)
            else:
                self.report({'WARNING'}, f"Server info not available: {self.server_name}")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to get server info: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_browse_mcp_templates(Operator):
    """Browse MCP server templates"""
    bl_idname = "ai_agent.browse_mcp_templates"
    bl_label = "Browse MCP Templates"
    bl_description = "Browse all available MCP server templates"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            server_manager = get_server_manager()
            templates = server_manager.list_templates()
            
            template_info = []
            for template_name in templates:
                template = server_manager.get_template(template_name)
                if template:
                    template_info.append(f"{template.name} ({template.category})")
            
            if template_info:
                info_message = f"Available templates: {', '.join(template_info)}"
                self.report({'INFO'}, info_message)
            else:
                self.report({'INFO'}, "No templates available")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to browse templates: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_refresh_context(Operator):
    """Refresh Blender context"""
    bl_idname = "ai_agent.refresh_context"
    bl_label = "Refresh Context"
    bl_description = "Refresh the current Blender context information"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            context_provider = get_context_provider()
            context_info = context_provider.get_current_context(force_refresh=True)
            
            self.report({'INFO'}, f"Context refreshed: {context_info.scene.objects_count} objects")
            logger.info("Blender context refreshed")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to refresh context: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_export_context(Operator):
    """Export Blender context"""
    bl_idname = "ai_agent.export_context"
    bl_label = "Export Context"
    bl_description = "Export current Blender context to JSON file"
    bl_options = {'REGISTER'}
    
    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Path to export context",
        default="blender_context.json",
        subtype='FILE_PATH'
    )
    
    def execute(self, context):
        try:
            context_provider = get_context_provider()
            json_str = context_provider.export_context_to_json(self.filepath)
            
            self.report({'INFO'}, f"Context exported to {self.filepath}")
            logger.info(f"Context exported to {self.filepath}")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to export context: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

# Classes to register
classes = [
    AI_AGENT_OT_add_mcp_server_from_template,
    AI_AGENT_OT_connect_mcp_server,
    AI_AGENT_OT_disconnect_mcp_server,
    AI_AGENT_OT_connect_all_mcp_servers,
    AI_AGENT_OT_execute_mcp_tool,
    AI_AGENT_OT_refresh_mcp_servers,
    AI_AGENT_OT_show_mcp_server_info,
    AI_AGENT_OT_browse_mcp_templates,
    AI_AGENT_OT_refresh_context,
    AI_AGENT_OT_export_context,
]

def register():
    """Register MCP operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister MCP operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
