"""
Context Provider - Provides Blender context and resources to MCP servers
"""

import bpy
import json
import time
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from pathlib import Path

from ..crew_system.blender_tools import get_blender_tools
from ..utils.logging_config import get_logger

logger = get_logger("context_provider")

@dataclass
class BlenderObjectInfo:
    """Information about a Blender object"""
    name: str
    type: str
    location: List[float]
    rotation: List[float]
    scale: List[float]
    visible: bool
    selected: bool
    active: bool
    material_count: int
    vertex_count: Optional[int] = None
    face_count: Optional[int] = None

@dataclass
class BlenderSceneInfo:
    """Information about the current Blender scene"""
    name: str
    frame_current: int
    frame_start: int
    frame_end: int
    render_engine: str
    resolution_x: int
    resolution_y: int
    objects_count: int
    meshes_count: int
    lights_count: int
    cameras_count: int
    materials_count: int
    collections_count: int

@dataclass
class BlenderContext:
    """Complete Blender context information"""
    scene: BlenderSceneInfo
    objects: List[BlenderObjectInfo]
    active_object: Optional[str]
    selected_objects: List[str]
    mode: str
    tool: str
    timestamp: float

class ContextProvider:
    """Provides Blender context and resources to MCP servers and AI agents"""
    
    def __init__(self):
        self.blender_tools = get_blender_tools()
        self.context_cache: Optional[BlenderContext] = None
        self.cache_timeout = 5.0  # seconds
        self.last_cache_update = 0.0
        
        # Resource tracking
        self.tracked_objects: Dict[str, Dict[str, Any]] = {}
        self.tracked_materials: Dict[str, Dict[str, Any]] = {}
        self.tracked_collections: Dict[str, Dict[str, Any]] = {}
    
    def get_current_context(self, force_refresh: bool = False) -> BlenderContext:
        """Get current Blender context"""
        try:
            current_time = time.time()
            
            # Use cache if valid and not forced refresh
            if (not force_refresh and 
                self.context_cache and 
                current_time - self.last_cache_update < self.cache_timeout):
                return self.context_cache
            
            # Gather scene information
            scene = bpy.context.scene
            scene_info = BlenderSceneInfo(
                name=scene.name,
                frame_current=scene.frame_current,
                frame_start=scene.frame_start,
                frame_end=scene.frame_end,
                render_engine=scene.render.engine,
                resolution_x=scene.render.resolution_x,
                resolution_y=scene.render.resolution_y,
                objects_count=len(scene.objects),
                meshes_count=len([obj for obj in scene.objects if obj.type == 'MESH']),
                lights_count=len([obj for obj in scene.objects if obj.type == 'LIGHT']),
                cameras_count=len([obj for obj in scene.objects if obj.type == 'CAMERA']),
                materials_count=len(bpy.data.materials),
                collections_count=len(bpy.data.collections)
            )
            
            # Gather object information
            objects_info = []
            for obj in scene.objects:
                obj_info = self._get_object_info(obj)
                objects_info.append(obj_info)
            
            # Get active and selected objects
            active_object = bpy.context.active_object.name if bpy.context.active_object else None
            selected_objects = [obj.name for obj in bpy.context.selected_objects]
            
            # Get current mode and tool
            mode = bpy.context.mode
            tool = bpy.context.workspace.tools.from_space_view3d_mode(mode).idname if hasattr(bpy.context, 'workspace') else 'UNKNOWN'
            
            # Create context
            context = BlenderContext(
                scene=scene_info,
                objects=objects_info,
                active_object=active_object,
                selected_objects=selected_objects,
                mode=mode,
                tool=tool,
                timestamp=current_time
            )
            
            # Update cache
            self.context_cache = context
            self.last_cache_update = current_time
            
            return context
            
        except Exception as e:
            logger.error(f"Failed to get Blender context: {e}")
            # Return minimal context on error
            return BlenderContext(
                scene=BlenderSceneInfo(
                    name="Unknown", frame_current=1, frame_start=1, frame_end=250,
                    render_engine="UNKNOWN", resolution_x=1920, resolution_y=1080,
                    objects_count=0, meshes_count=0, lights_count=0, cameras_count=0,
                    materials_count=0, collections_count=0
                ),
                objects=[],
                active_object=None,
                selected_objects=[],
                mode="UNKNOWN",
                tool="UNKNOWN",
                timestamp=time.time()
            )
    
    def _get_object_info(self, obj) -> BlenderObjectInfo:
        """Get information about a specific object"""
        try:
            # Basic object info
            obj_info = BlenderObjectInfo(
                name=obj.name,
                type=obj.type,
                location=list(obj.location),
                rotation=list(obj.rotation_euler),
                scale=list(obj.scale),
                visible=obj.visible_get(),
                selected=obj.select_get(),
                active=obj == bpy.context.active_object,
                material_count=len(obj.material_slots)
            )
            
            # Mesh-specific info
            if obj.type == 'MESH' and obj.data:
                obj_info.vertex_count = len(obj.data.vertices)
                obj_info.face_count = len(obj.data.polygons)
            
            return obj_info
            
        except Exception as e:
            logger.warning(f"Failed to get info for object {obj.name}: {e}")
            return BlenderObjectInfo(
                name=obj.name,
                type=obj.type,
                location=[0, 0, 0],
                rotation=[0, 0, 0],
                scale=[1, 1, 1],
                visible=True,
                selected=False,
                active=False,
                material_count=0
            )
    
    def get_context_summary(self) -> Dict[str, Any]:
        """Get a summary of the current context for AI agents"""
        try:
            context = self.get_current_context()
            
            summary = {
                "scene": {
                    "name": context.scene.name,
                    "current_frame": context.scene.frame_current,
                    "render_engine": context.scene.render_engine,
                    "resolution": [context.scene.resolution_x, context.scene.resolution_y]
                },
                "statistics": {
                    "total_objects": context.scene.objects_count,
                    "mesh_objects": context.scene.meshes_count,
                    "lights": context.scene.lights_count,
                    "cameras": context.scene.cameras_count,
                    "materials": context.scene.materials_count,
                    "collections": context.scene.collections_count
                },
                "selection": {
                    "active_object": context.active_object,
                    "selected_objects": context.selected_objects,
                    "selection_count": len(context.selected_objects)
                },
                "mode": context.mode,
                "tool": context.tool,
                "timestamp": context.timestamp
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to get context summary: {e}")
            return {"error": str(e)}
    
    def get_object_details(self, object_name: str) -> Optional[Dict[str, Any]]:
        """Get detailed information about a specific object"""
        try:
            obj = bpy.data.objects.get(object_name)
            if not obj:
                return None
            
            details = {
                "name": obj.name,
                "type": obj.type,
                "location": list(obj.location),
                "rotation": list(obj.rotation_euler),
                "scale": list(obj.scale),
                "dimensions": list(obj.dimensions),
                "visible": obj.visible_get(),
                "selected": obj.select_get(),
                "active": obj == bpy.context.active_object,
                "parent": obj.parent.name if obj.parent else None,
                "children": [child.name for child in obj.children],
                "material_slots": len(obj.material_slots),
                "modifiers": [mod.name for mod in obj.modifiers] if hasattr(obj, 'modifiers') else []
            }
            
            # Type-specific details
            if obj.type == 'MESH' and obj.data:
                details["mesh"] = {
                    "vertices": len(obj.data.vertices),
                    "edges": len(obj.data.edges),
                    "faces": len(obj.data.polygons),
                    "materials": [slot.material.name if slot.material else None for slot in obj.material_slots]
                }
            
            elif obj.type == 'LIGHT' and obj.data:
                details["light"] = {
                    "type": obj.data.type,
                    "energy": obj.data.energy,
                    "color": list(obj.data.color)
                }
            
            elif obj.type == 'CAMERA' and obj.data:
                details["camera"] = {
                    "type": obj.data.type,
                    "focal_length": obj.data.lens,
                    "sensor_width": obj.data.sensor_width
                }
            
            return details
            
        except Exception as e:
            logger.error(f"Failed to get object details for {object_name}: {e}")
            return None
    
    def get_material_info(self, material_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a material"""
        try:
            material = bpy.data.materials.get(material_name)
            if not material:
                return None
            
            info = {
                "name": material.name,
                "use_nodes": material.use_nodes,
                "users": material.users
            }
            
            # Get principled BSDF info if using nodes
            if material.use_nodes and material.node_tree:
                principled = None
                for node in material.node_tree.nodes:
                    if node.type == 'BSDF_PRINCIPLED':
                        principled = node
                        break
                
                if principled:
                    info["principled_bsdf"] = {
                        "base_color": list(principled.inputs["Base Color"].default_value),
                        "metallic": principled.inputs["Metallic"].default_value,
                        "roughness": principled.inputs["Roughness"].default_value,
                        "ior": principled.inputs["IOR"].default_value if "IOR" in principled.inputs else 1.45
                    }
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get material info for {material_name}: {e}")
            return None
    
    def get_collection_info(self, collection_name: str) -> Optional[Dict[str, Any]]:
        """Get information about a collection"""
        try:
            collection = bpy.data.collections.get(collection_name)
            if not collection:
                return None
            
            info = {
                "name": collection.name,
                "objects": [obj.name for obj in collection.objects],
                "children": [child.name for child in collection.children],
                "hide_viewport": collection.hide_viewport,
                "hide_render": collection.hide_render
            }
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get collection info for {collection_name}: {e}")
            return None
    
    def export_context_to_json(self, filepath: Optional[str] = None) -> str:
        """Export current context to JSON"""
        try:
            context = self.get_current_context(force_refresh=True)
            
            # Convert to dict for JSON serialization
            context_dict = asdict(context)
            
            # Create JSON string
            json_str = json.dumps(context_dict, indent=2, default=str)
            
            # Save to file if filepath provided
            if filepath:
                with open(filepath, 'w', encoding='utf-8') as f:
                    f.write(json_str)
                logger.info(f"Context exported to {filepath}")
            
            return json_str
            
        except Exception as e:
            logger.error(f"Failed to export context to JSON: {e}")
            return json.dumps({"error": str(e)})
    
    def get_available_resources(self) -> Dict[str, List[str]]:
        """Get list of available Blender resources"""
        try:
            resources = {
                "objects": [obj.name for obj in bpy.data.objects],
                "meshes": [mesh.name for mesh in bpy.data.meshes],
                "materials": [mat.name for mat in bpy.data.materials],
                "textures": [tex.name for tex in bpy.data.textures],
                "images": [img.name for img in bpy.data.images],
                "lights": [light.name for light in bpy.data.lights],
                "cameras": [cam.name for cam in bpy.data.cameras],
                "collections": [col.name for col in bpy.data.collections],
                "scenes": [scene.name for scene in bpy.data.scenes],
                "worlds": [world.name for world in bpy.data.worlds]
            }
            
            return resources
            
        except Exception as e:
            logger.error(f"Failed to get available resources: {e}")
            return {}
    
    def track_changes(self) -> Dict[str, Any]:
        """Track changes in Blender since last check"""
        try:
            current_context = self.get_current_context()
            changes = {
                "objects_added": [],
                "objects_removed": [],
                "objects_modified": [],
                "materials_added": [],
                "materials_removed": [],
                "selection_changed": False,
                "mode_changed": False
            }
            
            # TODO: Implement change tracking logic
            # This would require storing previous state and comparing
            
            return changes
            
        except Exception as e:
            logger.error(f"Failed to track changes: {e}")
            return {"error": str(e)}

# Global context provider instance
_context_provider = ContextProvider()

def get_context_provider():
    """Get the context provider instance"""
    return _context_provider

def register():
    """Register context provider"""
    pass

def unregister():
    """Unregister context provider"""
    pass
