"""
Blender Tools - Specialized tools for AI agents to interact with <PERSON><PERSON><PERSON>
"""

import bpy
import bmesh
import mathutils
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

from ..utils.logging_config import get_logger

logger = get_logger("blender_tools")

@dataclass
class ToolResult:
    """Result of a tool operation"""
    success: bool
    message: str
    data: Any = None
    error: Optional[str] = None

class BlenderTools:
    """Collection of Blender-specific tools for AI agents"""
    
    def __init__(self):
        self.context = bpy.context
        self.data = bpy.data
        self.ops = bpy.ops
    
    # === MESH MANIPULATION TOOLS ===
    
    def create_primitive_mesh(self, mesh_type: str = "cube", location: Tuple[float, float, float] = (0, 0, 0), 
                            scale: Tuple[float, float, float] = (1, 1, 1), **kwargs) -> ToolResult:
        """Create a primitive mesh object"""
        try:
            # Clear selection
            bpy.ops.object.select_all(action='DESELECT')
            
            # Create primitive based on type
            if mesh_type.lower() == "cube":
                bpy.ops.mesh.primitive_cube_add(location=location, scale=scale)
            elif mesh_type.lower() == "sphere":
                subdivisions = kwargs.get('subdivisions', 2)
                bpy.ops.mesh.primitive_uv_sphere_add(location=location, scale=scale, subdivisions=subdivisions)
            elif mesh_type.lower() == "cylinder":
                vertices = kwargs.get('vertices', 32)
                bpy.ops.mesh.primitive_cylinder_add(location=location, scale=scale, vertices=vertices)
            elif mesh_type.lower() == "plane":
                bpy.ops.mesh.primitive_plane_add(location=location, scale=scale)
            elif mesh_type.lower() == "monkey":
                bpy.ops.mesh.primitive_monkey_add(location=location, scale=scale)
            else:
                return ToolResult(False, f"Unknown mesh type: {mesh_type}")
            
            obj = self.context.active_object
            return ToolResult(True, f"Created {mesh_type} mesh: {obj.name}", {"object": obj.name})
            
        except Exception as e:
            logger.error(f"Failed to create primitive mesh: {e}")
            return ToolResult(False, "Failed to create mesh", error=str(e))
    
    def apply_modifier(self, object_name: str, modifier_type: str, **kwargs) -> ToolResult:
        """Apply a modifier to an object"""
        try:
            obj = self.data.objects.get(object_name)
            if not obj:
                return ToolResult(False, f"Object not found: {object_name}")
            
            # Select and make active
            bpy.ops.object.select_all(action='DESELECT')
            obj.select_set(True)
            self.context.view_layer.objects.active = obj
            
            # Add modifier based on type
            if modifier_type.lower() == "subdivision":
                levels = kwargs.get('levels', 2)
                modifier = obj.modifiers.new(name="Subdivision", type='SUBSURF')
                modifier.levels = levels
                
            elif modifier_type.lower() == "mirror":
                axis = kwargs.get('axis', 'X')
                modifier = obj.modifiers.new(name="Mirror", type='MIRROR')
                modifier.use_axis[0] = 'X' in axis.upper()
                modifier.use_axis[1] = 'Y' in axis.upper()
                modifier.use_axis[2] = 'Z' in axis.upper()
                
            elif modifier_type.lower() == "bevel":
                width = kwargs.get('width', 0.1)
                segments = kwargs.get('segments', 2)
                modifier = obj.modifiers.new(name="Bevel", type='BEVEL')
                modifier.width = width
                modifier.segments = segments
                
            elif modifier_type.lower() == "array":
                count = kwargs.get('count', 3)
                offset = kwargs.get('offset', 2.0)
                modifier = obj.modifiers.new(name="Array", type='ARRAY')
                modifier.count = count
                modifier.relative_offset_displace[0] = offset
                
            else:
                return ToolResult(False, f"Unknown modifier type: {modifier_type}")
            
            return ToolResult(True, f"Applied {modifier_type} modifier to {object_name}", 
                            {"modifier": modifier.name})
            
        except Exception as e:
            logger.error(f"Failed to apply modifier: {e}")
            return ToolResult(False, "Failed to apply modifier", error=str(e))
    
    def extrude_faces(self, object_name: str, distance: float = 1.0, face_indices: Optional[List[int]] = None) -> ToolResult:
        """Extrude faces of a mesh"""
        try:
            obj = self.data.objects.get(object_name)
            if not obj or obj.type != 'MESH':
                return ToolResult(False, f"Mesh object not found: {object_name}")
            
            # Enter edit mode
            bpy.ops.object.select_all(action='DESELECT')
            obj.select_set(True)
            self.context.view_layer.objects.active = obj
            bpy.ops.object.mode_set(mode='EDIT')
            
            # Select faces
            bpy.ops.mesh.select_all(action='DESELECT')
            if face_indices:
                bm = bmesh.from_mesh(obj.data)
                for i in face_indices:
                    if i < len(bm.faces):
                        bm.faces[i].select = True
                bmesh.update_edit_mesh(obj.data)
            else:
                bpy.ops.mesh.select_all(action='SELECT')
            
            # Extrude
            bpy.ops.mesh.extrude_region_move(TRANSFORM_OT_translate={"value": (0, 0, distance)})
            
            # Return to object mode
            bpy.ops.object.mode_set(mode='OBJECT')
            
            return ToolResult(True, f"Extruded faces on {object_name}", {"distance": distance})
            
        except Exception as e:
            logger.error(f"Failed to extrude faces: {e}")
            return ToolResult(False, "Failed to extrude faces", error=str(e))
    
    # === MATERIAL AND TEXTURE TOOLS ===
    
    def create_material(self, name: str, base_color: Tuple[float, float, float, float] = (0.8, 0.8, 0.8, 1.0),
                       metallic: float = 0.0, roughness: float = 0.5) -> ToolResult:
        """Create a new material"""
        try:
            # Create material
            material = self.data.materials.new(name=name)
            material.use_nodes = True
            
            # Get principled BSDF node
            nodes = material.node_tree.nodes
            principled = nodes.get("Principled BSDF")
            
            if principled:
                principled.inputs["Base Color"].default_value = base_color
                principled.inputs["Metallic"].default_value = metallic
                principled.inputs["Roughness"].default_value = roughness
            
            return ToolResult(True, f"Created material: {name}", {"material": name})
            
        except Exception as e:
            logger.error(f"Failed to create material: {e}")
            return ToolResult(False, "Failed to create material", error=str(e))
    
    def assign_material(self, object_name: str, material_name: str) -> ToolResult:
        """Assign a material to an object"""
        try:
            obj = self.data.objects.get(object_name)
            material = self.data.materials.get(material_name)
            
            if not obj:
                return ToolResult(False, f"Object not found: {object_name}")
            if not material:
                return ToolResult(False, f"Material not found: {material_name}")
            
            # Assign material
            if obj.data.materials:
                obj.data.materials[0] = material
            else:
                obj.data.materials.append(material)
            
            return ToolResult(True, f"Assigned material {material_name} to {object_name}")
            
        except Exception as e:
            logger.error(f"Failed to assign material: {e}")
            return ToolResult(False, "Failed to assign material", error=str(e))
    
    # === LIGHTING TOOLS ===
    
    def add_light(self, light_type: str = "SUN", location: Tuple[float, float, float] = (0, 0, 5),
                 energy: float = 5.0, color: Tuple[float, float, float] = (1.0, 1.0, 1.0)) -> ToolResult:
        """Add a light to the scene"""
        try:
            bpy.ops.object.light_add(type=light_type.upper(), location=location)
            light_obj = self.context.active_object
            light_data = light_obj.data
            
            light_data.energy = energy
            light_data.color = color
            
            return ToolResult(True, f"Added {light_type} light: {light_obj.name}", 
                            {"light": light_obj.name})
            
        except Exception as e:
            logger.error(f"Failed to add light: {e}")
            return ToolResult(False, "Failed to add light", error=str(e))
    
    def setup_hdri_lighting(self, hdri_path: str, strength: float = 1.0) -> ToolResult:
        """Setup HDRI environment lighting"""
        try:
            # Enable nodes for world
            world = self.context.scene.world
            world.use_nodes = True
            
            # Get world nodes
            nodes = world.node_tree.nodes
            links = world.node_tree.links
            
            # Clear existing nodes
            nodes.clear()
            
            # Add environment texture node
            env_node = nodes.new(type='ShaderNodeTexEnvironment')
            
            # Add background shader
            bg_node = nodes.new(type='ShaderNodeBackground')
            bg_node.inputs["Strength"].default_value = strength
            
            # Add world output
            output_node = nodes.new(type='ShaderNodeOutputWorld')
            
            # Link nodes
            links.new(env_node.outputs["Color"], bg_node.inputs["Color"])
            links.new(bg_node.outputs["Background"], output_node.inputs["Surface"])
            
            # Load HDRI image
            if hdri_path:
                try:
                    hdri_image = self.data.images.load(hdri_path)
                    env_node.image = hdri_image
                except:
                    logger.warning(f"Could not load HDRI image: {hdri_path}")
            
            return ToolResult(True, "Setup HDRI lighting", {"strength": strength})
            
        except Exception as e:
            logger.error(f"Failed to setup HDRI lighting: {e}")
            return ToolResult(False, "Failed to setup HDRI lighting", error=str(e))
    
    # === ANIMATION TOOLS ===
    
    def add_keyframe(self, object_name: str, property_path: str, frame: int, value: Any) -> ToolResult:
        """Add a keyframe to an object property"""
        try:
            obj = self.data.objects.get(object_name)
            if not obj:
                return ToolResult(False, f"Object not found: {object_name}")
            
            # Set current frame
            self.context.scene.frame_set(frame)
            
            # Set property value
            if hasattr(obj, property_path):
                setattr(obj, property_path, value)
                obj.keyframe_insert(data_path=property_path, frame=frame)
            else:
                return ToolResult(False, f"Property not found: {property_path}")
            
            return ToolResult(True, f"Added keyframe for {object_name}.{property_path} at frame {frame}")
            
        except Exception as e:
            logger.error(f"Failed to add keyframe: {e}")
            return ToolResult(False, "Failed to add keyframe", error=str(e))
    
    # === SCENE MANAGEMENT TOOLS ===
    
    def organize_objects_in_collection(self, object_names: List[str], collection_name: str) -> ToolResult:
        """Organize objects into a collection"""
        try:
            # Create collection if it doesn't exist
            collection = self.data.collections.get(collection_name)
            if not collection:
                collection = self.data.collections.new(collection_name)
                self.context.scene.collection.children.link(collection)
            
            # Move objects to collection
            moved_objects = []
            for obj_name in object_names:
                obj = self.data.objects.get(obj_name)
                if obj:
                    # Remove from current collections
                    for coll in obj.users_collection:
                        coll.objects.unlink(obj)
                    
                    # Add to new collection
                    collection.objects.link(obj)
                    moved_objects.append(obj_name)
            
            return ToolResult(True, f"Organized {len(moved_objects)} objects in collection {collection_name}",
                            {"collection": collection_name, "objects": moved_objects})
            
        except Exception as e:
            logger.error(f"Failed to organize objects: {e}")
            return ToolResult(False, "Failed to organize objects", error=str(e))
    
    def cleanup_scene(self, remove_default: bool = True) -> ToolResult:
        """Clean up the scene by removing default objects"""
        try:
            removed_objects = []
            
            if remove_default:
                default_objects = ["Cube", "Light", "Camera"]
                for obj_name in default_objects:
                    obj = self.data.objects.get(obj_name)
                    if obj:
                        self.data.objects.remove(obj, do_unlink=True)
                        removed_objects.append(obj_name)
            
            return ToolResult(True, f"Cleaned up scene, removed {len(removed_objects)} objects",
                            {"removed": removed_objects})
            
        except Exception as e:
            logger.error(f"Failed to cleanup scene: {e}")
            return ToolResult(False, "Failed to cleanup scene", error=str(e))
    
    # === RENDER TOOLS ===
    
    def setup_render_settings(self, engine: str = "CYCLES", samples: int = 128, 
                            resolution: Tuple[int, int] = (1920, 1080)) -> ToolResult:
        """Setup render settings"""
        try:
            scene = self.context.scene
            
            # Set render engine
            scene.render.engine = engine.upper()
            
            # Set resolution
            scene.render.resolution_x = resolution[0]
            scene.render.resolution_y = resolution[1]
            
            # Set samples for Cycles
            if engine.upper() == "CYCLES":
                scene.cycles.samples = samples
            
            return ToolResult(True, f"Setup render settings: {engine}, {samples} samples, {resolution}",
                            {"engine": engine, "samples": samples, "resolution": resolution})
            
        except Exception as e:
            logger.error(f"Failed to setup render settings: {e}")
            return ToolResult(False, "Failed to setup render settings", error=str(e))
    
    def render_image(self, filepath: str = "", use_viewport: bool = False) -> ToolResult:
        """Render the current scene"""
        try:
            if filepath:
                self.context.scene.render.filepath = filepath
            
            if use_viewport:
                bpy.ops.render.opengl(write_still=True)
            else:
                bpy.ops.render.render(write_still=True)
            
            return ToolResult(True, f"Rendered image to {filepath or 'default location'}")
            
        except Exception as e:
            logger.error(f"Failed to render image: {e}")
            return ToolResult(False, "Failed to render image", error=str(e))
    
    # === UTILITY METHODS ===
    
    def get_scene_info(self) -> Dict[str, Any]:
        """Get information about the current scene"""
        scene = self.context.scene
        
        return {
            "objects_count": len(scene.objects),
            "meshes_count": len([obj for obj in scene.objects if obj.type == 'MESH']),
            "lights_count": len([obj for obj in scene.objects if obj.type == 'LIGHT']),
            "cameras_count": len([obj for obj in scene.objects if obj.type == 'CAMERA']),
            "materials_count": len(self.data.materials),
            "collections_count": len(self.data.collections),
            "current_frame": scene.frame_current,
            "frame_range": (scene.frame_start, scene.frame_end)
        }

# Global Blender tools instance
_blender_tools = BlenderTools()

def get_blender_tools():
    """Get the Blender tools instance"""
    return _blender_tools

def register():
    """Register Blender tools"""
    pass

def unregister():
    """Unregister Blender tools"""
    pass
