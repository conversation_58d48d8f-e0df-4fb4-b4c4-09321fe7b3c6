"""
Chat System - Natural language interface for AI Agent System
"""

from .nlp_processor import get_nlp_processor
from .chat_interface import get_chat_interface
from .agent_bridge import get_agent_bridge
from .suggestions import get_suggestions_system

# List of modules to register
modules = [
    'nlp_processor',
    'chat_interface', 
    'agent_bridge',
    'suggestions'
]

def register():
    """Register chat system modules"""
    try:
        # Initialize NLP processor
        nlp_processor = get_nlp_processor()
        nlp_processor.initialize()
        
        # Initialize chat interface
        chat_interface = get_chat_interface()
        chat_interface.initialize()
        
        # Initialize agent bridge
        agent_bridge = get_agent_bridge()
        agent_bridge.initialize()
        
        # Initialize suggestions system
        suggestions = get_suggestions_system()
        suggestions.initialize()
        
        print("✅ Chat system initialized successfully")
        
    except Exception as e:
        print(f"❌ Failed to initialize chat system: {e}")

def unregister():
    """Unregister chat system modules"""
    try:
        # Cleanup in reverse order
        get_suggestions_system().cleanup()
        get_agent_bridge().cleanup()
        get_chat_interface().cleanup()
        get_nlp_processor().cleanup()
        
        print("✅ Chat system cleaned up successfully")
        
    except Exception as e:
        print(f"❌ Failed to cleanup chat system: {e}")
