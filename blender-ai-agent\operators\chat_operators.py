"""
Chat Operators - Blender operators for chat interface functionality
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty

from ..chat_system.chat_interface import get_chat_interface
from ..chat_system.suggestions import get_suggestions_system
from ..chat_system.agent_bridge import get_agent_bridge
from ..utils.logging_config import get_logger

logger = get_logger("chat_operators")

class AI_AGENT_OT_send_chat_message(Operator):
    """Send message to AI assistant"""
    bl_idname = "ai_agent.send_chat_message"
    bl_label = "Send Message"
    bl_description = "Send message to AI assistant"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            chat_interface = get_chat_interface()
            message_text = context.scene.chat_props.message_input.strip()
            
            if not message_text:
                self.report({'WARNING'}, "Lütfen bir mesaj yazın")
                return {'CANCELLED'}
            
            # Add user message to chat
            chat_interface.add_user_message(message_text)
            
            # Clear input field
            context.scene.chat_props.message_input = ""
            
            # Refresh UI
            for area in context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Mesaj gönderilemedi: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_execute_suggestion(Operator):
    """Execute a smart suggestion"""
    bl_idname = "ai_agent.execute_suggestion"
    bl_label = "Execute Suggestion"
    bl_description = "Execute a smart suggestion"
    bl_options = {'REGISTER'}
    
    suggestion_id: StringProperty(
        name="Suggestion ID",
        description="ID of the suggestion to execute"
    )
    
    def execute(self, context):
        try:
            suggestions_system = get_suggestions_system()
            result = suggestions_system.execute_suggestion(self.suggestion_id)
            
            if result['success']:
                # Send the command as a chat message
                chat_interface = get_chat_interface()
                command = result['command']
                
                # Add user message
                chat_interface.add_user_message(command)
                
                # Refresh UI
                for area in context.screen.areas:
                    if area.type == 'VIEW_3D':
                        area.tag_redraw()
                
                return {'FINISHED'}
            else:
                self.report({'ERROR'}, f"Öneri çalıştırılamadı: {result['error']}")
                return {'CANCELLED'}
                
        except Exception as e:
            error_msg = f"Öneri çalıştırılamadı: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_suggestion_info(Operator):
    """Show suggestion information"""
    bl_idname = "ai_agent.suggestion_info"
    bl_label = "Suggestion Info"
    bl_description = "Show detailed information about a suggestion"
    bl_options = {'REGISTER'}
    
    suggestion_id: StringProperty(
        name="Suggestion ID",
        description="ID of the suggestion"
    )
    
    description: StringProperty(
        name="Description",
        description="Suggestion description"
    )
    
    def execute(self, context):
        self.report({'INFO'}, self.description)
        return {'FINISHED'}

class AI_AGENT_OT_clear_chat(Operator):
    """Clear chat history"""
    bl_idname = "ai_agent.clear_chat"
    bl_label = "Clear Chat"
    bl_description = "Clear all chat messages"
    bl_options = {'REGISTER'}
    
    def invoke(self, context, event):
        return context.window_manager.invoke_confirm(self, event)
    
    def execute(self, context):
        try:
            chat_interface = get_chat_interface()
            chat_interface.clear_messages()
            
            # Add welcome message back
            chat_interface.add_system_message(
                "🤖 Chat temizlendi! Size nasıl yardım edebilirim?"
            )
            
            # Refresh UI
            for area in context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
            
            self.report({'INFO'}, "Chat geçmişi temizlendi")
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Chat temizlenemedi: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_export_chat(Operator):
    """Export chat conversation"""
    bl_idname = "ai_agent.export_chat"
    bl_label = "Export Chat"
    bl_description = "Export chat conversation to file"
    bl_options = {'REGISTER'}
    
    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Path to export chat",
        default="chat_conversation.json",
        subtype='FILE_PATH'
    )
    
    def execute(self, context):
        try:
            chat_interface = get_chat_interface()
            conversation_data = chat_interface.export_conversation()
            
            import json
            with open(self.filepath, 'w', encoding='utf-8') as f:
                json.dump(conversation_data, f, indent=2, ensure_ascii=False)
            
            self.report({'INFO'}, f"Chat {self.filepath} dosyasına kaydedildi")
            logger.info(f"Chat exported to {self.filepath}")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Chat dışa aktarılamadı: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}
    
    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class AI_AGENT_OT_cancel_task(Operator):
    """Cancel an active task"""
    bl_idname = "ai_agent.cancel_task"
    bl_label = "Cancel Task"
    bl_description = "Cancel an active agent task"
    bl_options = {'REGISTER'}
    
    task_id: StringProperty(
        name="Task ID",
        description="ID of the task to cancel"
    )
    
    def execute(self, context):
        try:
            agent_bridge = get_agent_bridge()
            success = agent_bridge.cancel_task(self.task_id)
            
            if success:
                self.report({'INFO'}, "Görev iptal edildi")
                
                # Add system message to chat
                chat_interface = get_chat_interface()
                chat_interface.add_system_message("🚫 Görev iptal edildi")
                
                # Refresh UI
                for area in context.screen.areas:
                    if area.type == 'VIEW_3D':
                        area.tag_redraw()
                
                return {'FINISHED'}
            else:
                self.report({'WARNING'}, "Görev iptal edilemedi")
                return {'CANCELLED'}
                
        except Exception as e:
            error_msg = f"Görev iptal edilemedi: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_chat_settings(Operator):
    """Open chat settings"""
    bl_idname = "ai_agent.chat_settings"
    bl_label = "Chat Settings"
    bl_description = "Open chat settings panel"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        # For now, just show a message
        self.report({'INFO'}, "Chat ayarları yakında eklenecek")
        return {'FINISHED'}

class AI_AGENT_OT_voice_input(Operator):
    """Voice input (future feature)"""
    bl_idname = "ai_agent.voice_input"
    bl_label = "Voice Input"
    bl_description = "Voice input for chat (coming soon)"
    bl_options = {'REGISTER'}
    
    enabled: bpy.props.BoolProperty(default=False)
    
    def execute(self, context):
        self.report({'INFO'}, "Sesli giriş özelliği yakında eklenecek")
        return {'FINISHED'}

class AI_AGENT_OT_refresh_suggestions(Operator):
    """Refresh smart suggestions"""
    bl_idname = "ai_agent.refresh_suggestions"
    bl_label = "Refresh Suggestions"
    bl_description = "Refresh context-aware suggestions"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            # Refresh UI to update suggestions
            for area in context.screen.areas:
                if area.type == 'VIEW_3D':
                    area.tag_redraw()
            
            self.report({'INFO'}, "Öneriler güncellendi")
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Öneriler güncellenemedi: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_quick_help(Operator):
    """Show quick help for chat interface"""
    bl_idname = "ai_agent.quick_help"
    bl_label = "Quick Help"
    bl_description = "Show quick help for using the chat interface"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        chat_interface = get_chat_interface()
        
        help_message = (
            "🆘 AI Assistant Yardım\n\n"
            "💬 Örnekler:\n"
            "• 'Küp oluştur'\n"
            "• 'Bu obje için materyal yap'\n"
            "• 'Sahneyi aydınlat'\n"
            "• 'Karakterimi animasyonlu yap'\n\n"
            "💡 İpuçları:\n"
            "• Doğal dilde konuşun\n"
            "• Obje seçerek daha iyi sonuçlar alın\n"
            "• Hızlı aksiyonları kullanın"
        )
        
        chat_interface.add_system_message(help_message)
        
        # Refresh UI
        for area in context.screen.areas:
            if area.type == 'VIEW_3D':
                area.tag_redraw()
        
        return {'FINISHED'}

# Classes to register
classes = [
    AI_AGENT_OT_send_chat_message,
    AI_AGENT_OT_execute_suggestion,
    AI_AGENT_OT_suggestion_info,
    AI_AGENT_OT_clear_chat,
    AI_AGENT_OT_export_chat,
    AI_AGENT_OT_cancel_task,
    AI_AGENT_OT_chat_settings,
    AI_AGENT_OT_voice_input,
    AI_AGENT_OT_refresh_suggestions,
    AI_AGENT_OT_quick_help,
]

def register():
    """Register chat operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister chat operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
