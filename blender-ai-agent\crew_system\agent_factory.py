"""
Agent Factory - Creates and manages AI agents with predefined roles and capabilities
"""

import json
from pathlib import Path
from typing import Dict, List, Optional, Any
from dataclasses import dataclass

from ..utils.logging_config import get_logger

logger = get_logger("agent_factory")

@dataclass
class AgentTemplate:
    """Template for creating agents"""
    role: str
    goal: str
    backstory: str
    tools: List[str]
    capabilities: List[str]
    specialization: str
    verbose: bool = True
    allow_delegation: bool = False
    max_iter: int = 10
    memory: bool = True

class AgentFactory:
    """Factory for creating specialized AI agents for Blender workflows"""
    
    def __init__(self):
        self.agent_templates: Dict[str, AgentTemplate] = {}
        self.custom_templates: Dict[str, AgentTemplate] = {}
        self._load_default_templates()
        self._load_custom_templates()
    
    def _load_default_templates(self):
        """Load default agent templates"""
        # 3D Modeling Specialist
        self.agent_templates['modeler'] = AgentTemplate(
            role='3D Modeling Specialist',
            goal='Create high-quality 3D models based on specifications and requirements',
            backstory='''You are an expert 3D artist with 10+ years of experience in creating 
            detailed and optimized 3D models. You understand topology, edge flow, and 
            optimization techniques for different use cases including games, animation, 
            and architectural visualization.''',
            tools=['mesh_creator', 'modifier_tool', 'topology_optimizer', 'uv_mapper'],
            capabilities=['mesh_creation', 'retopology', 'uv_mapping', 'optimization'],
            specialization='modeling'
        )
        
        # Material and Texture Artist
        self.agent_templates['material_artist'] = AgentTemplate(
            role='Material and Texture Artist',
            goal='Create realistic and stylized materials and textures for 3D objects',
            backstory='''You are a skilled material artist who understands PBR workflows, 
            shader networks, and texture painting. You can create both photorealistic 
            and stylized materials that enhance the visual quality of 3D scenes.''',
            tools=['material_creator', 'texture_painter', 'node_editor', 'image_processor'],
            capabilities=['material_creation', 'texture_painting', 'shader_development', 'pbr_workflow'],
            specialization='materials'
        )
        
        # Animation Specialist
        self.agent_templates['animator'] = AgentTemplate(
            role='Animation Specialist',
            goal='Create smooth and expressive animations for characters and objects',
            backstory='''You are an experienced animator who understands the principles 
            of animation, rigging, and character movement. You can create both realistic 
            and stylized animations that bring 3D scenes to life.''',
            tools=['keyframe_animator', 'rig_creator', 'constraint_manager', 'curve_editor'],
            capabilities=['keyframe_animation', 'rigging', 'character_animation', 'motion_graphics'],
            specialization='animation'
        )
        
        # Lighting Artist
        self.agent_templates['lighting_artist'] = AgentTemplate(
            role='Lighting Artist',
            goal='Create compelling lighting setups that enhance mood and visual storytelling',
            backstory='''You are a lighting specialist who understands color theory, 
            mood creation, and technical aspects of 3D lighting. You can create both 
            realistic and artistic lighting setups for various scenarios.''',
            tools=['light_manager', 'hdri_loader', 'shadow_controller', 'color_grader'],
            capabilities=['lighting_design', 'mood_creation', 'hdri_setup', 'render_optimization'],
            specialization='lighting'
        )
        
        # Render Specialist
        self.agent_templates['render_specialist'] = AgentTemplate(
            role='Render Specialist',
            goal='Optimize render settings and produce high-quality final images',
            backstory='''You are a render expert who understands different render engines, 
            optimization techniques, and post-processing workflows. You can balance 
            quality and render times to achieve the best results.''',
            tools=['render_optimizer', 'compositor', 'denoiser', 'batch_renderer'],
            capabilities=['render_optimization', 'compositing', 'post_processing', 'batch_rendering'],
            specialization='rendering'
        )
        
        # Scene Manager
        self.agent_templates['scene_manager'] = AgentTemplate(
            role='Scene Manager',
            goal='Organize and manage complex 3D scenes with proper hierarchy and optimization',
            backstory='''You are a technical artist who specializes in scene organization, 
            optimization, and workflow management. You ensure scenes are well-structured, 
            performant, and easy to work with.''',
            tools=['outliner_manager', 'collection_organizer', 'instance_manager', 'cleanup_tool'],
            capabilities=['scene_organization', 'optimization', 'asset_management', 'workflow_design'],
            specialization='scene_management'
        )
        
        # General Assistant
        self.agent_templates['assistant'] = AgentTemplate(
            role='Blender Assistant',
            goal='Provide general help and guidance for Blender workflows',
            backstory='''You are a knowledgeable Blender assistant who can help with 
            various tasks, answer questions, and provide guidance on best practices. 
            You have broad knowledge across all Blender features.''',
            tools=['help_system', 'tutorial_finder', 'best_practices', 'troubleshooter'],
            capabilities=['general_help', 'troubleshooting', 'tutorials', 'best_practices'],
            specialization='general'
        )
        
        logger.info(f"Loaded {len(self.agent_templates)} default agent templates")
    
    def _load_custom_templates(self):
        """Load custom agent templates from files"""
        try:
            templates_dir = Path(__file__).parent.parent / "templates" / "agents"
            
            if not templates_dir.exists():
                logger.info("No custom agent templates directory found")
                return
            
            for template_file in templates_dir.glob("*.json"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        template_data = json.load(f)
                    
                    template_name = template_file.stem
                    template = AgentTemplate(**template_data)
                    self.custom_templates[template_name] = template
                    
                    logger.debug(f"Loaded custom agent template: {template_name}")
                    
                except Exception as e:
                    logger.error(f"Failed to load custom template {template_file}: {e}")
            
            logger.info(f"Loaded {len(self.custom_templates)} custom agent templates")
            
        except Exception as e:
            logger.error(f"Failed to load custom agent templates: {e}")
    
    def get_template(self, template_name: str) -> Optional[AgentTemplate]:
        """Get an agent template by name"""
        # Check custom templates first
        if template_name in self.custom_templates:
            return self.custom_templates[template_name]
        
        # Then check default templates
        if template_name in self.agent_templates:
            return self.agent_templates[template_name]
        
        logger.warning(f"Agent template not found: {template_name}")
        return None
    
    def list_templates(self) -> Dict[str, List[str]]:
        """List all available agent templates"""
        return {
            'default': list(self.agent_templates.keys()),
            'custom': list(self.custom_templates.keys())
        }
    
    def create_agent_config(self, template_name: str, customizations: Optional[Dict] = None) -> Optional[Dict]:
        """Create agent configuration from template"""
        template = self.get_template(template_name)
        if not template:
            return None
        
        config = {
            'role': template.role,
            'goal': template.goal,
            'backstory': template.backstory,
            'tools': template.tools.copy(),
            'capabilities': template.capabilities.copy(),
            'specialization': template.specialization,
            'verbose': template.verbose,
            'allow_delegation': template.allow_delegation,
            'max_iter': template.max_iter,
            'memory': template.memory
        }
        
        # Apply customizations
        if customizations:
            for key, value in customizations.items():
                if key in config:
                    config[key] = value
                else:
                    logger.warning(f"Unknown customization key: {key}")
        
        logger.info(f"Created agent config for: {template.role}")
        return config
    
    def create_crew_config(self, crew_type: str, agents: List[str], tasks: Optional[List[Dict]] = None) -> Dict:
        """Create a complete crew configuration"""
        crew_config = {
            'id': f"{crew_type}_crew",
            'type': crew_type,
            'agents': [],
            'tasks': tasks or [],
            'process': 'sequential',
            'verbose': True
        }
        
        # Create agent configurations
        for agent_template in agents:
            agent_config = self.create_agent_config(agent_template)
            if agent_config:
                crew_config['agents'].append(agent_config)
        
        # Add default tasks if none provided
        if not crew_config['tasks']:
            crew_config['tasks'] = self._create_default_tasks(crew_type, agents)
        
        logger.info(f"Created crew config: {crew_type} with {len(crew_config['agents'])} agents")
        return crew_config
    
    def _create_default_tasks(self, crew_type: str, agents: List[str]) -> List[Dict]:
        """Create default tasks based on crew type and agents"""
        tasks = []
        
        if crew_type == 'modeling':
            tasks = [
                {
                    'description': 'Analyze the modeling requirements and create a base mesh',
                    'agent_role': '3D Modeling Specialist',
                    'expected_output': 'A base mesh that meets the specified requirements'
                },
                {
                    'description': 'Optimize the mesh topology and add necessary details',
                    'agent_role': '3D Modeling Specialist',
                    'expected_output': 'An optimized, detailed 3D model ready for texturing'
                }
            ]
        
        elif crew_type == 'complete_asset':
            tasks = [
                {
                    'description': 'Create the base 3D model according to specifications',
                    'agent_role': '3D Modeling Specialist',
                    'expected_output': 'A complete 3D model with proper topology'
                },
                {
                    'description': 'Create materials and textures for the model',
                    'agent_role': 'Material and Texture Artist',
                    'expected_output': 'Realistic materials and textures applied to the model'
                },
                {
                    'description': 'Set up lighting for the scene',
                    'agent_role': 'Lighting Artist',
                    'expected_output': 'Professional lighting setup that enhances the model'
                },
                {
                    'description': 'Render the final images with optimal settings',
                    'agent_role': 'Render Specialist',
                    'expected_output': 'High-quality rendered images of the asset'
                }
            ]
        
        elif crew_type == 'animation':
            tasks = [
                {
                    'description': 'Set up rigging for the character or object',
                    'agent_role': 'Animation Specialist',
                    'expected_output': 'A properly rigged character ready for animation'
                },
                {
                    'description': 'Create keyframe animation according to specifications',
                    'agent_role': 'Animation Specialist',
                    'expected_output': 'Smooth, expressive animation that meets requirements'
                }
            ]
        
        return tasks
    
    def save_custom_template(self, name: str, template: AgentTemplate) -> bool:
        """Save a custom agent template"""
        try:
            templates_dir = Path(__file__).parent.parent / "templates" / "agents"
            templates_dir.mkdir(parents=True, exist_ok=True)
            
            template_file = templates_dir / f"{name}.json"
            template_data = {
                'role': template.role,
                'goal': template.goal,
                'backstory': template.backstory,
                'tools': template.tools,
                'capabilities': template.capabilities,
                'specialization': template.specialization,
                'verbose': template.verbose,
                'allow_delegation': template.allow_delegation,
                'max_iter': template.max_iter,
                'memory': template.memory
            }
            
            with open(template_file, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, indent=2)
            
            self.custom_templates[name] = template
            logger.info(f"Saved custom agent template: {name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save custom template {name}: {e}")
            return False
    
    def get_agent_capabilities(self, template_name: str) -> List[str]:
        """Get capabilities of an agent template"""
        template = self.get_template(template_name)
        return template.capabilities if template else []
    
    def get_recommended_crew(self, task_type: str) -> Optional[Dict]:
        """Get recommended crew configuration for a task type"""
        recommendations = {
            'modeling': {
                'agents': ['modeler'],
                'description': 'Single agent crew for 3D modeling tasks'
            },
            'texturing': {
                'agents': ['material_artist'],
                'description': 'Single agent crew for material and texture work'
            },
            'animation': {
                'agents': ['animator'],
                'description': 'Single agent crew for animation tasks'
            },
            'complete_asset': {
                'agents': ['modeler', 'material_artist', 'lighting_artist', 'render_specialist'],
                'description': 'Full pipeline crew for complete asset creation'
            },
            'scene_setup': {
                'agents': ['scene_manager', 'lighting_artist'],
                'description': 'Crew for organizing and lighting scenes'
            },
            'rendering': {
                'agents': ['lighting_artist', 'render_specialist'],
                'description': 'Crew specialized in lighting and rendering'
            }
        }
        
        if task_type in recommendations:
            rec = recommendations[task_type]
            return self.create_crew_config(task_type, rec['agents'])
        
        return None

# Global agent factory instance
_agent_factory = AgentFactory()

def get_agent_factory():
    """Get the agent factory instance"""
    return _agent_factory

def register():
    """Register agent factory"""
    pass

def unregister():
    """Unregister agent factory"""
    pass
