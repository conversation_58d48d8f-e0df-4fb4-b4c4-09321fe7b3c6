"""
Crew System Module - Crew AI integration for Blender
"""

from . import crew_manager
from . import agent_factory
from . import task_executor
from . import blender_tools

# List of modules to register
modules = [
    crew_manager,
    agent_factory,
    task_executor,
    blender_tools,
]

def register():
    """Register all crew system components"""
    for module in modules:
        if hasattr(module, 'register'):
            module.register()

def unregister():
    """Unregister all crew system components"""
    for module in reversed(modules):
        if hasattr(module, 'unregister'):
            module.unregister()
