"""
Demo Script - LLM Integration Test
Bu script yeni LLM tabanlı NLP processor'ı test eder
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from llm_integration import LLMConfig, LLMProviderType
from chat_system.llm_nlp_processor import LLMNLPProcessor
from chat_system.prompt_templates import TurkishPromptTemplates
from config.llm_config import LLMConfigManager

class LLMDemo:
    """LLM Integration Demo"""
    
    def __init__(self):
        self.config_manager = LLMConfigManager()
        self.processor: LLMNLPProcessor = None
        
    async def setup(self):
        """Setup demo environment"""
        print("🚀 LLM Integration Demo Başlatılıyor...")
        print("=" * 50)
        
        # Load or create config
        if not self.config_manager.load_config():
            print("❌ Konfigürasyon yüklenemedi")
            return False
        
        # Get current config
        llm_config = self.config_manager.get_current_config()
        if not llm_config:
            print("⚠️  LLM konfigürasyonu bulunamadı, varsayılan ayarlar kullanılıyor...")
            
            # Create test config (requires API key)
            api_key = input("OpenAI API Key girin (test için): ").strip()
            if not api_key:
                print("❌ API key gerekli")
                return False
            
            llm_config = LLMConfig(
                provider_type=LLMProviderType.OPENAI,
                model="gpt-4o-mini",
                api_key=api_key,
                temperature=0.7,
                max_tokens=1500
            )
            
            self.config_manager.set_custom_config(
                provider_type=llm_config.provider_type,
                model=llm_config.model,
                api_key=llm_config.api_key,
                temperature=llm_config.temperature,
                max_tokens=llm_config.max_tokens
            )
        
        # Validate config
        is_valid, message = self.config_manager.validate_config(llm_config)
        if not is_valid:
            print(f"❌ Konfigürasyon geçersiz: {message}")
            return False
        
        print(f"✅ LLM Konfigürasyonu: {llm_config.provider_type.value} - {llm_config.model}")
        
        # Initialize processor
        self.processor = LLMNLPProcessor(llm_config)
        if not await self.processor.initialize():
            print("❌ LLM Processor başlatılamadı")
            return False
        
        print("✅ LLM Processor başarıyla başlatıldı")
        return True
    
    def show_config_info(self):
        """Show configuration information"""
        print("\n📋 Konfigürasyon Bilgileri:")
        print("-" * 30)
        
        summary = self.config_manager.get_config_summary()
        for key, value in summary.items():
            print(f"{key}: {value}")
        
        print("\n📦 Mevcut Presetler:")
        presets = self.config_manager.get_presets()
        for name, preset in presets.items():
            print(f"  • {preset.name} ({name}): {preset.description}")
    
    def show_prompt_examples(self):
        """Show Turkish prompt examples"""
        print("\n🇹🇷 Türkçe Komut Örnekleri:")
        print("-" * 30)
        
        examples = TurkishPromptTemplates.get_intent_examples()
        for intent, intent_examples in examples.items():
            print(f"\n{intent.upper()}:")
            for example in intent_examples[:3]:  # Show first 3 examples
                print(f"  • {example}")
    
    async def test_message_parsing(self, message: str):
        """Test message parsing"""
        print(f"\n🔍 Mesaj Analizi: '{message}'")
        print("-" * 50)
        
        # Mock Blender context
        context = {
            "scene_name": "Demo Scene",
            "mode": "OBJECT",
            "selected_objects": ["Cube"],
            "active_object": "Cube",
            "total_objects": 3
        }
        
        try:
            # Parse message
            parsed = await self.processor.parse_message(message, context)
            
            print(f"Intent: {parsed.intent.value}")
            print(f"Confidence: {parsed.confidence:.2f}")
            print(f"Entities: {json.dumps(parsed.entities, indent=2, ensure_ascii=False)}")
            print(f"Parameters: {json.dumps(parsed.parameters, indent=2, ensure_ascii=False)}")
            
            if parsed.reasoning:
                print(f"Reasoning: {parsed.reasoning}")
            
            if parsed.suggested_actions:
                print("Suggested Actions:")
                for action in parsed.suggested_actions:
                    print(f"  • {action}")
            
            # Generate response
            response = await self.processor.generate_response(parsed)
            print(f"\nResponse: {response}")
            
        except Exception as e:
            print(f"❌ Hata: {e}")
    
    async def interactive_demo(self):
        """Interactive demo mode"""
        print("\n🎮 İnteraktif Demo Modu")
        print("Türkçe komutlar girin (çıkmak için 'quit' yazın)")
        print("-" * 50)
        
        while True:
            try:
                user_input = input("\n> ").strip()
                
                if user_input.lower() in ['quit', 'exit', 'çık']:
                    break
                
                if not user_input:
                    continue
                
                await self.test_message_parsing(user_input)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Hata: {e}")
        
        print("\n👋 Demo sonlandırıldı")
    
    async def run_predefined_tests(self):
        """Run predefined test cases"""
        print("\n🧪 Önceden Tanımlı Testler")
        print("-" * 30)
        
        test_cases = [
            "Kırmızı bir küp oluştur",
            "Seçili objeyi 2 kat büyüt",
            "Küreyi sağa taşı",
            "Metal materyal ekle",
            "Sahneyi aydınlat",
            "Render al",
            "Yardım et",
            "Nasıl animasyon yapılır?"
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 Test {i}/{len(test_cases)}")
            await self.test_message_parsing(test_case)
            
            # Wait for user input to continue
            input("\nDevam etmek için Enter'a basın...")
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.processor:
            await self.processor.cleanup()
        print("🧹 Kaynaklar temizlendi")

async def main():
    """Main demo function"""
    demo = LLMDemo()
    
    try:
        # Setup
        if not await demo.setup():
            return
        
        # Show info
        demo.show_config_info()
        demo.show_prompt_examples()
        
        # Choose demo mode
        print("\n🎯 Demo Modu Seçin:")
        print("1. Önceden tanımlı testler")
        print("2. İnteraktif mod")
        
        choice = input("\nSeçiminiz (1 veya 2): ").strip()
        
        if choice == "1":
            await demo.run_predefined_tests()
        elif choice == "2":
            await demo.interactive_demo()
        else:
            print("❌ Geçersiz seçim")
        
    except Exception as e:
        print(f"❌ Demo hatası: {e}")
    
    finally:
        await demo.cleanup()

if __name__ == "__main__":
    print("🤖 Blender AI Agent - LLM Integration Demo")
    print("=" * 50)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n👋 Demo kullanıcı tarafından sonlandırıldı")
    except Exception as e:
        print(f"\n❌ Beklenmeyen hata: {e}")
