"""
Crew Manager - Core Crew AI orchestration and management
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from pathlib import Path
import json

# Import Crew AI components (will be available after dependency installation)
try:
    from crewai import Agent, Task, Crew, Process
    from crewai.tools import BaseTool
    CREWAI_AVAILABLE = True
except ImportError:
    # Fallback classes for when Crew AI is not installed
    class Agent:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class Task:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class Crew:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
        
        def kickoff(self):
            return "Crew AI not installed"
    
    class Process:
        sequential = "sequential"
        hierarchical = "hierarchical"
    
    class BaseTool:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    CREWAI_AVAILABLE = False

from ..core.config_manager import get_config_manager
from ..utils.logging_config import get_logger

logger = get_logger("crew_manager")

class CrewManager:
    """Manages Crew AI crews, agents, and tasks"""
    
    def __init__(self):
        self.config_manager = get_config_manager()
        self.active_crews: Dict[str, Crew] = {}
        self.crew_templates: Dict[str, Dict] = {}
        self.execution_results: Dict[str, Any] = {}
        self.is_initialized = False
        
        # Load crew templates
        self._load_crew_templates()
    
    def initialize(self):
        """Initialize the crew manager"""
        try:
            if not CREWAI_AVAILABLE:
                logger.warning("Crew AI not available. Please install dependencies.")
                return False
            
            logger.info("Initializing Crew Manager...")
            
            # Validate configuration
            if not self._validate_config():
                logger.error("Invalid configuration for Crew AI")
                return False
            
            # Setup LLM provider
            if not self._setup_llm_provider():
                logger.error("Failed to setup LLM provider")
                return False
            
            self.is_initialized = True
            logger.info("Crew Manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Crew Manager: {e}")
            return False
    
    def _validate_config(self) -> bool:
        """Validate crew configuration"""
        required_settings = [
            'ai.default_provider',
            'ai.model',
            'crew.enabled'
        ]
        
        for setting in required_settings:
            value = self.config_manager.get(setting)
            if value is None:
                logger.error(f"Missing required setting: {setting}")
                return False
        
        return True
    
    def _setup_llm_provider(self) -> bool:
        """Setup LLM provider based on configuration"""
        try:
            provider = self.config_manager.get('ai.default_provider', 'openai')
            model = self.config_manager.get('ai.model', 'gpt-4')
            
            logger.info(f"Setting up LLM provider: {provider} with model: {model}")
            
            # TODO: Implement actual LLM provider setup
            # This will depend on the specific provider (OpenAI, Anthropic, local, etc.)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to setup LLM provider: {e}")
            return False
    
    def _load_crew_templates(self):
        """Load crew templates from files"""
        try:
            templates_dir = Path(__file__).parent.parent / "templates" / "crews"
            
            if not templates_dir.exists():
                logger.info("No crew templates directory found")
                return
            
            for template_file in templates_dir.glob("*.json"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        template_data = json.load(f)
                    
                    template_name = template_file.stem
                    self.crew_templates[template_name] = template_data
                    logger.debug(f"Loaded crew template: {template_name}")
                    
                except Exception as e:
                    logger.error(f"Failed to load template {template_file}: {e}")
            
            logger.info(f"Loaded {len(self.crew_templates)} crew templates")
            
        except Exception as e:
            logger.error(f"Failed to load crew templates: {e}")
    
    def create_crew(self, crew_config: Dict) -> Optional[str]:
        """Create a new crew from configuration"""
        try:
            if not self.is_initialized:
                logger.error("Crew Manager not initialized")
                return None
            
            crew_id = crew_config.get('id', f"crew_{len(self.active_crews)}")
            
            # Create agents
            agents = []
            for agent_config in crew_config.get('agents', []):
                agent = self._create_agent(agent_config)
                if agent:
                    agents.append(agent)
            
            if not agents:
                logger.error("No valid agents created for crew")
                return None
            
            # Create tasks
            tasks = []
            for task_config in crew_config.get('tasks', []):
                task = self._create_task(task_config, agents)
                if task:
                    tasks.append(task)
            
            if not tasks:
                logger.error("No valid tasks created for crew")
                return None
            
            # Create crew
            process = getattr(Process, crew_config.get('process', 'sequential'))
            
            crew = Crew(
                agents=agents,
                tasks=tasks,
                process=process,
                verbose=crew_config.get('verbose', True)
            )
            
            self.active_crews[crew_id] = crew
            logger.info(f"Created crew: {crew_id} with {len(agents)} agents and {len(tasks)} tasks")
            
            return crew_id
            
        except Exception as e:
            logger.error(f"Failed to create crew: {e}")
            return None
    
    def _create_agent(self, agent_config: Dict) -> Optional[Agent]:
        """Create an agent from configuration"""
        try:
            # Import tools for the agent
            tools = []
            for tool_name in agent_config.get('tools', []):
                tool = self._get_tool(tool_name)
                if tool:
                    tools.append(tool)
            
            agent = Agent(
                role=agent_config.get('role', 'Assistant'),
                goal=agent_config.get('goal', 'Help with tasks'),
                backstory=agent_config.get('backstory', 'An AI assistant'),
                tools=tools,
                verbose=agent_config.get('verbose', True),
                allow_delegation=agent_config.get('allow_delegation', False)
            )
            
            logger.debug(f"Created agent: {agent.role}")
            return agent
            
        except Exception as e:
            logger.error(f"Failed to create agent: {e}")
            return None
    
    def _create_task(self, task_config: Dict, agents: List[Agent]) -> Optional[Task]:
        """Create a task from configuration"""
        try:
            # Find the agent for this task
            agent_role = task_config.get('agent_role')
            agent = None
            
            if agent_role:
                for a in agents:
                    if a.role == agent_role:
                        agent = a
                        break
            
            if not agent and agents:
                agent = agents[0]  # Default to first agent
            
            if not agent:
                logger.error("No agent available for task")
                return None
            
            task = Task(
                description=task_config.get('description', 'Complete the task'),
                agent=agent,
                expected_output=task_config.get('expected_output', 'Task completed successfully')
            )
            
            logger.debug(f"Created task for agent: {agent.role}")
            return task
            
        except Exception as e:
            logger.error(f"Failed to create task: {e}")
            return None
    
    def _get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get a tool by name"""
        # TODO: Implement tool registry and retrieval
        # This will be expanded when we implement Blender-specific tools
        logger.debug(f"Requested tool: {tool_name}")
        return None
    
    def execute_crew(self, crew_id: str) -> Optional[str]:
        """Execute a crew and return result ID"""
        try:
            if crew_id not in self.active_crews:
                logger.error(f"Crew not found: {crew_id}")
                return None
            
            crew = self.active_crews[crew_id]
            logger.info(f"Executing crew: {crew_id}")
            
            # Execute crew
            result = crew.kickoff()
            
            # Store result
            result_id = f"result_{crew_id}_{len(self.execution_results)}"
            self.execution_results[result_id] = {
                'crew_id': crew_id,
                'result': result,
                'timestamp': logger.handlers[0].formatter.formatTime(logger.makeRecord('', 0, '', 0, '', (), None))
            }
            
            logger.info(f"Crew execution completed: {crew_id}")
            return result_id
            
        except Exception as e:
            logger.error(f"Failed to execute crew {crew_id}: {e}")
            return None
    
    def get_crew_status(self, crew_id: str) -> Dict:
        """Get status of a crew"""
        if crew_id not in self.active_crews:
            return {'status': 'not_found'}
        
        return {
            'status': 'active',
            'crew_id': crew_id,
            'agents_count': len(self.active_crews[crew_id].agents),
            'tasks_count': len(self.active_crews[crew_id].tasks)
        }
    
    def get_execution_result(self, result_id: str) -> Optional[Dict]:
        """Get execution result by ID"""
        return self.execution_results.get(result_id)
    
    def list_active_crews(self) -> List[str]:
        """List all active crew IDs"""
        return list(self.active_crews.keys())
    
    def remove_crew(self, crew_id: str) -> bool:
        """Remove a crew"""
        if crew_id in self.active_crews:
            del self.active_crews[crew_id]
            logger.info(f"Removed crew: {crew_id}")
            return True
        return False
    
    def cleanup(self):
        """Cleanup crew manager resources"""
        self.active_crews.clear()
        self.execution_results.clear()
        self.is_initialized = False
        logger.info("Crew Manager cleaned up")

# Global crew manager instance
_crew_manager = CrewManager()

def get_crew_manager():
    """Get the crew manager instance"""
    return _crew_manager

def register():
    """Register crew manager"""
    _crew_manager.initialize()

def unregister():
    """Unregister crew manager"""
    _crew_manager.cleanup()
