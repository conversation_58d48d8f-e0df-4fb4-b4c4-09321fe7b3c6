"""
Addon Manager - Core addon lifecycle management
"""

import bpy
import sys
import os
from pathlib import Path
import logging
from .dependency_manager import get_dependency_manager

logger = logging.getLogger(__name__)

class AddonManager:
    """Manages addon lifecycle, dependencies, and core functionality"""
    
    def __init__(self):
        self.addon_dir = Path(__file__).parent.parent
        self.is_initialized = False
        self.dependencies_installed = False
        
    def initialize(self):
        """Initialize the addon system"""
        try:
            logger.info("Initializing AI Agent System...")
            
            # Check Blender version compatibility
            if not self._check_blender_compatibility():
                raise RuntimeError("Blender version not compatible")
            
            # Setup addon directories
            self._setup_directories()
            
            # Check dependencies
            self._check_dependencies()
            
            self.is_initialized = True
            logger.info("AI Agent System initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize addon: {e}")
            raise
    
    def cleanup(self):
        """Cleanup addon resources"""
        try:
            logger.info("Cleaning up AI Agent System...")
            
            # Stop any running processes
            self._stop_background_processes()
            
            # Clear caches
            self._clear_caches()
            
            self.is_initialized = False
            logger.info("AI Agent System cleaned up successfully")
            
        except Exception as e:
            logger.error(f"Failed to cleanup addon: {e}")
    
    def _check_blender_compatibility(self):
        """Check if current Blender version is compatible"""
        version = bpy.app.version
        min_version = (4, 4, 0)
        
        if version < min_version:
            logger.error(f"Blender {version} is not compatible. Minimum required: {min_version}")
            return False
        
        logger.info(f"Blender {version} is compatible")
        return True
    
    def _setup_directories(self):
        """Setup required directories"""
        directories = [
            self.addon_dir / "temp",
            self.addon_dir / "logs",
            self.addon_dir / "cache",
            self.addon_dir / "configs"
        ]
        
        for directory in directories:
            directory.mkdir(exist_ok=True)
            logger.debug(f"Created directory: {directory}")
    
    def _check_dependencies(self):
        """Check if required dependencies are available"""
        dependency_manager = get_dependency_manager()

        # Check basic Python modules first
        required_modules = [
            "json",
            "asyncio",
            "threading",
            "urllib.request"
        ]

        missing_modules = []
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                missing_modules.append(module)

        if missing_modules:
            logger.warning(f"Missing basic modules: {missing_modules}")
            self.dependencies_installed = False
            return

        # Check addon-specific dependencies
        self.dependencies_installed = dependency_manager.check_dependencies()

        if self.dependencies_installed:
            logger.info("All dependencies available")
        else:
            logger.warning("Some addon dependencies are missing")
    
    def _stop_background_processes(self):
        """Stop any running background processes"""
        # TODO: Implement background process management
        pass
    
    def _clear_caches(self):
        """Clear addon caches"""
        cache_dir = self.addon_dir / "cache"
        if cache_dir.exists():
            for file in cache_dir.glob("*"):
                try:
                    file.unlink()
                except Exception as e:
                    logger.warning(f"Failed to clear cache file {file}: {e}")

# Global addon manager instance
_addon_manager = AddonManager()

def initialize():
    """Initialize the addon manager"""
    _addon_manager.initialize()

def cleanup():
    """Cleanup the addon manager"""
    _addon_manager.cleanup()

def get_manager():
    """Get the addon manager instance"""
    return _addon_manager
