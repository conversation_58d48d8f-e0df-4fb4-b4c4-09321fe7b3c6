# Blender Icon Reference for AI Agent System

This document provides a reference for all icons used in the AI Agent System addon, ensuring consistency with <PERSON><PERSON><PERSON>'s official icon set.

## System Icons

### Main System
- **AI Agent System**: `OUTLINER_OB_ARMATURE` - Represents the main AI agent system
- **Agents**: `ARMATURE_DATA` - Individual AI agents
- **Crews**: `OUTLINER_COLLECTION` - Groups of agents working together
- **Tasks**: `SEQUENCE` - Individual tasks and workflows

### Status Icons
- **Success/Connected**: `CHECKMARK` - Successful operations
- **Error/Failed**: `ERROR` - Failed operations or errors
- **Warning**: `ERROR` - Warning states
- **Information**: `INFO` - Informational messages
- **Pending**: `TIME` - Waiting/pending operations
- **Running**: `PLAY` - Active/running operations
- **Cancelled**: `PANEL_CLOSE` - Cancelled operations
- **Unknown**: `QUESTION` - Unknown or undefined states

## MCP (Model Context Protocol) Icons

### Server Management
- **MCP Servers**: `NETWORK_DRIVE` - MCP server instances
- **Server Templates**: `NETWORK_DRIVE` - Server configuration templates
- **Connected**: `LINKED` - Connected servers
- **Disconnected**: `UNLINKED` - Disconnected servers
- **Connecting**: `TIME` - Servers in connection process

### Tools and Context
- **Tools**: `TOOL_SETTINGS` - General tools
- **Context**: `SCENE_DATA` - Blender context information
- **Resources**: `OUTLINER_DATA_MESH` - Available resources

## Tool Category Icons

### Blender-Specific Tools
- **Mesh Operations**: `OUTLINER_OB_MESH` - Mesh creation and editing
- **Materials**: `MATERIAL_DATA` - Material creation and assignment
- **Lighting**: `OUTLINER_OB_LIGHT` - Light setup and configuration
- **Animation**: `ARMATURE_DATA` - Animation and rigging
- **Rendering**: `RENDER_STILL` - Render settings and operations
- **Scene Management**: `SCENE_DATA` - Scene organization

### General Tools
- **Filesystem**: `FILE_FOLDER` - File system operations
- **Web/Network**: `URL` - Web and network operations
- **Database**: `DATABASE` - Database operations
- **Development**: `CONSOLE` - Development and coding tools
- **Utilities**: `TOOL_SETTINGS` - General utility tools
- **Custom**: `PREFERENCES` - Custom user tools

## UI Action Icons

### Basic Actions
- **Add/Create**: `ADD` - Create new items
- **Remove/Delete**: `REMOVE` - Remove items
- **Execute/Run**: `PLAY` - Execute operations
- **Stop/Pause**: `PAUSE` - Pause operations
- **Cancel**: `PANEL_CLOSE` - Cancel operations
- **Refresh**: `FILE_REFRESH` - Refresh/reload data

### Navigation and View
- **Browse/Zoom**: `ZOOM_ALL` - Browse or view all items
- **Export**: `EXPORT` - Export data
- **Import**: `IMPORT` - Import data
- **Settings**: `PREFERENCES` - Settings and preferences

## Crew Type Icons

### Specialized Crews
- **Modeling Crew**: `OUTLINER_OB_MESH` - 3D modeling operations
- **Complete Asset Crew**: `SCENE_DATA` - Full asset creation pipeline
- **Animation Crew**: `ARMATURE_DATA` - Animation workflows
- **Custom Crew**: `PREFERENCES` - User-defined crews

## Performance and Monitoring

### Statistics and Analysis
- **Performance**: `GRAPH` - Performance monitoring
- **Statistics**: `GRAPH` - Statistical information
- **Logs**: `TEXT` - Log files and text output
- **Cleanup**: `TRASH` - Cleanup operations

## Configuration and Settings

### System Configuration
- **Preferences**: `PREFERENCES` - System preferences
- **Settings**: `PREFERENCES` - General settings
- **Dependencies**: `PREFERENCES` - Dependency management
- **Advanced Settings**: `PREFERENCES` - Advanced configuration

## Notes

### Icon Selection Guidelines
1. **Consistency**: Always use official Blender icons from the API
2. **Context**: Choose icons that match the functional context
3. **Recognition**: Prefer commonly recognized icons over obscure ones
4. **Hierarchy**: Use similar icons for related functionality

### Common Icon Patterns
- `OUTLINER_*` icons for object types and hierarchical data
- `*_DATA` icons for data types (materials, armatures, etc.)
- Action verbs use simple icons (`PLAY`, `PAUSE`, `ADD`, `REMOVE`)
- Status uses clear visual indicators (`CHECKMARK`, `ERROR`, `TIME`)

### Fallback Icons
- Unknown states: `QUESTION`
- Generic tools: `TOOL_SETTINGS`
- Generic data: `FILE_BLANK`
- Generic objects: `OBJECT_DATA`

## Reference Links
- [Blender Python API Icon Items](https://docs.blender.org/api/current/bpy_types_enum_items/icon_items.html)
- [Blender UI Layout Documentation](https://docs.blender.org/api/current/bpy.types.UILayout.html)

---
*Last updated: December 2024*
*AI Agent System v1.2.0*
