"""
Task Executor - Handles execution of AI agent tasks with Blender context
"""

import asyncio
import threading
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

from ..utils.logging_config import get_logger

logger = get_logger("task_executor")

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class TaskResult:
    """Result of task execution"""
    task_id: str
    status: TaskStatus
    result: Any = None
    error: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    duration: Optional[float] = None

class TaskExecutor:
    """Executes AI agent tasks with proper Blender context management"""
    
    def __init__(self):
        self.active_tasks: Dict[str, TaskResult] = {}
        self.task_queue: List[Dict] = []
        self.max_concurrent_tasks = 3
        self.is_running = False
        self.executor_thread: Optional[threading.Thread] = None
        self.progress_callbacks: Dict[str, Callable] = {}
        
    def start(self):
        """Start the task executor"""
        if self.is_running:
            logger.warning("Task executor already running")
            return
        
        self.is_running = True
        self.executor_thread = threading.Thread(target=self._executor_loop, daemon=True)
        self.executor_thread.start()
        logger.info("Task executor started")
    
    def stop(self):
        """Stop the task executor"""
        self.is_running = False
        if self.executor_thread:
            self.executor_thread.join(timeout=5.0)
        logger.info("Task executor stopped")
    
    def submit_task(self, task_config: Dict, progress_callback: Optional[Callable] = None) -> str:
        """Submit a task for execution"""
        task_id = f"task_{int(time.time() * 1000)}_{len(self.active_tasks)}"
        
        # Create task result
        task_result = TaskResult(
            task_id=task_id,
            status=TaskStatus.PENDING
        )
        
        self.active_tasks[task_id] = task_result
        
        # Add progress callback if provided
        if progress_callback:
            self.progress_callbacks[task_id] = progress_callback
        
        # Add to queue
        task_config['task_id'] = task_id
        self.task_queue.append(task_config)
        
        logger.info(f"Task submitted: {task_id}")
        return task_id
    
    def get_task_status(self, task_id: str) -> Optional[TaskResult]:
        """Get status of a task"""
        return self.active_tasks.get(task_id)
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a pending or running task"""
        if task_id not in self.active_tasks:
            return False
        
        task_result = self.active_tasks[task_id]
        
        if task_result.status == TaskStatus.PENDING:
            # Remove from queue
            self.task_queue = [t for t in self.task_queue if t.get('task_id') != task_id]
            task_result.status = TaskStatus.CANCELLED
            logger.info(f"Task cancelled: {task_id}")
            return True
        
        elif task_result.status == TaskStatus.RUNNING:
            # Mark for cancellation (actual cancellation depends on task implementation)
            task_result.status = TaskStatus.CANCELLED
            logger.info(f"Task marked for cancellation: {task_id}")
            return True
        
        return False
    
    def _executor_loop(self):
        """Main executor loop"""
        logger.info("Task executor loop started")
        
        while self.is_running:
            try:
                # Process pending tasks
                if self.task_queue and self._get_running_task_count() < self.max_concurrent_tasks:
                    task_config = self.task_queue.pop(0)
                    self._execute_task(task_config)
                
                # Small delay to prevent busy waiting
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in executor loop: {e}")
        
        logger.info("Task executor loop ended")
    
    def _get_running_task_count(self) -> int:
        """Get number of currently running tasks"""
        return sum(1 for task in self.active_tasks.values() if task.status == TaskStatus.RUNNING)
    
    def _execute_task(self, task_config: Dict):
        """Execute a single task"""
        task_id = task_config['task_id']
        task_result = self.active_tasks[task_id]
        
        try:
            logger.info(f"Starting task execution: {task_id}")
            
            # Update status
            task_result.status = TaskStatus.RUNNING
            task_result.start_time = time.time()
            
            # Notify progress callback
            self._notify_progress(task_id, "started", 0)
            
            # Execute the actual task
            result = self._run_task(task_config)
            
            # Update result
            task_result.result = result
            task_result.status = TaskStatus.COMPLETED
            task_result.end_time = time.time()
            task_result.duration = task_result.end_time - task_result.start_time
            
            # Notify completion
            self._notify_progress(task_id, "completed", 100)
            
            logger.info(f"Task completed: {task_id} in {task_result.duration:.2f}s")
            
        except Exception as e:
            # Handle task failure
            task_result.status = TaskStatus.FAILED
            task_result.error = str(e)
            task_result.end_time = time.time()
            if task_result.start_time:
                task_result.duration = task_result.end_time - task_result.start_time
            
            # Notify failure
            self._notify_progress(task_id, f"failed: {e}", -1)
            
            logger.error(f"Task failed: {task_id} - {e}")
    
    def _run_task(self, task_config: Dict) -> Any:
        """Run the actual task (to be implemented based on task type)"""
        task_type = task_config.get('type', 'unknown')
        task_id = task_config['task_id']
        
        logger.debug(f"Running task type: {task_type}")
        
        # Simulate task execution for now
        # In a real implementation, this would:
        # 1. Parse the task configuration
        # 2. Set up Blender context
        # 3. Execute the appropriate Blender operations
        # 4. Return the result
        
        if task_type == 'crew_execution':
            return self._execute_crew_task(task_config)
        elif task_type == 'blender_operation':
            return self._execute_blender_operation(task_config)
        else:
            # Default simulation
            import random
            duration = random.uniform(1, 5)  # Simulate 1-5 second task
            
            for i in range(10):
                if self.active_tasks[task_id].status == TaskStatus.CANCELLED:
                    raise Exception("Task was cancelled")
                
                time.sleep(duration / 10)
                progress = (i + 1) * 10
                self._notify_progress(task_id, f"processing step {i+1}/10", progress)
            
            return f"Task {task_type} completed successfully"
    
    def _execute_crew_task(self, task_config: Dict) -> Any:
        """Execute a crew AI task"""
        try:
            from .crew_manager import get_crew_manager
            
            crew_manager = get_crew_manager()
            crew_config = task_config.get('crew_config')
            
            if not crew_config:
                raise ValueError("No crew configuration provided")
            
            # Create and execute crew
            crew_id = crew_manager.create_crew(crew_config)
            if not crew_id:
                raise Exception("Failed to create crew")
            
            result_id = crew_manager.execute_crew(crew_id)
            if not result_id:
                raise Exception("Failed to execute crew")
            
            # Get result
            result = crew_manager.get_execution_result(result_id)
            
            # Cleanup crew
            crew_manager.remove_crew(crew_id)
            
            return result
            
        except Exception as e:
            logger.error(f"Crew task execution failed: {e}")
            raise
    
    def _execute_blender_operation(self, task_config: Dict) -> Any:
        """Execute a Blender operation"""
        try:
            operation = task_config.get('operation')
            parameters = task_config.get('parameters', {})
            
            if not operation:
                raise ValueError("No operation specified")
            
            # Import Blender tools
            from .blender_tools import get_blender_tools
            
            tools = get_blender_tools()
            
            # Execute operation
            if hasattr(tools, operation):
                method = getattr(tools, operation)
                result = method(**parameters)
                return result
            else:
                raise ValueError(f"Unknown operation: {operation}")
                
        except Exception as e:
            logger.error(f"Blender operation failed: {e}")
            raise
    
    def _notify_progress(self, task_id: str, message: str, progress: int):
        """Notify progress callback"""
        if task_id in self.progress_callbacks:
            try:
                self.progress_callbacks[task_id](task_id, message, progress)
            except Exception as e:
                logger.error(f"Progress callback error for {task_id}: {e}")
    
    def get_active_tasks(self) -> Dict[str, TaskResult]:
        """Get all active tasks"""
        return self.active_tasks.copy()
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """Clean up old completed tasks"""
        current_time = time.time()
        max_age_seconds = max_age_hours * 3600
        
        tasks_to_remove = []
        for task_id, task_result in self.active_tasks.items():
            if (task_result.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED] and
                task_result.end_time and 
                current_time - task_result.end_time > max_age_seconds):
                tasks_to_remove.append(task_id)
        
        for task_id in tasks_to_remove:
            del self.active_tasks[task_id]
            if task_id in self.progress_callbacks:
                del self.progress_callbacks[task_id]
        
        if tasks_to_remove:
            logger.info(f"Cleaned up {len(tasks_to_remove)} old tasks")

# Global task executor instance
_task_executor = TaskExecutor()

def get_task_executor():
    """Get the task executor instance"""
    return _task_executor

def register():
    """Register task executor"""
    _task_executor.start()

def unregister():
    """Unregister task executor"""
    _task_executor.stop()
