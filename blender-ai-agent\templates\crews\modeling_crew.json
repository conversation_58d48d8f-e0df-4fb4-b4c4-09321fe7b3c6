{"id": "modeling_crew", "name": "3D Modeling Crew", "description": "Specialized crew for 3D modeling tasks", "agents": [{"role": "3D Modeling Specialist", "goal": "Create high-quality 3D models based on specifications and requirements", "backstory": "You are an expert 3D artist with 10+ years of experience in creating detailed and optimized 3D models. You understand topology, edge flow, and optimization techniques for different use cases including games, animation, and architectural visualization.", "tools": ["mesh_creator", "modifier_tool", "topology_optimizer", "uv_mapper"], "verbose": true, "allow_delegation": false}], "tasks": [{"description": "Analyze the modeling requirements and create a base mesh according to specifications", "agent_role": "3D Modeling Specialist", "expected_output": "A base mesh that meets the specified requirements with proper topology"}, {"description": "Optimize the mesh topology and add necessary details for the final model", "agent_role": "3D Modeling Specialist", "expected_output": "An optimized, detailed 3D model ready for texturing or further processing"}], "process": "sequential", "verbose": true}