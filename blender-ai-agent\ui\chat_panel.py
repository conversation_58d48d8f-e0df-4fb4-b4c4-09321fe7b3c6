"""
Chat Panel - Modern chat interface for natural language AI interaction
"""

import bpy
from bpy.types import Panel, PropertyGroup, Operator
from bpy.props import String<PERSON>roperty, BoolProperty

from ..chat_system.chat_interface import get_chat_interface, MessageType
from ..chat_system.suggestions import get_suggestions_system
from ..chat_system.agent_bridge import get_agent_bridge

class AI_AGENT_PT_chat_panel(Panel):
    """Main chat interface panel"""
    bl_label = "AI Assistant"
    bl_idname = "AI_AGENT_PT_chat_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        chat_interface = get_chat_interface()
        
        # Chat header
        header = layout.row()
        header.label(text="🤖 AI Assistant", icon='OUTLINER_OB_ARMATURE')
        
        # Settings button
        settings_op = header.operator("ai_agent.chat_settings", text="", icon='PREFERENCES')
        
        # Chat messages area
        chat_box = layout.box()
        chat_box.scale_y = 0.8
        
        # Get recent messages
        messages = chat_interface.get_recent_messages(10)
        
        if not messages:
            chat_box.label(text="Merhaba! Size nasıl yardım edebilirim?", icon='INFO')
        else:
            # Display messages
            for message in messages[-8:]:  # Show last 8 messages
                self._draw_message(chat_box, message)
        
        # Typing indicator
        if chat_interface.is_typing:
            typing_row = chat_box.row()
            typing_row.label(text="🤖 Yazıyor...", icon='TIME')
        
        # Input area
        input_box = layout.box()
        
        # Message input
        input_row = input_box.row()
        input_row.prop(context.scene.chat_props, "message_input", text="", placeholder="Mesajınızı yazın...")
        
        # Send button
        send_op = input_row.operator("ai_agent.send_chat_message", text="", icon='PLAY')
        
        # Quick actions row
        actions_row = input_box.row()
        actions_row.scale_y = 0.8
        
        # Voice input (future feature)
        actions_row.operator("ai_agent.voice_input", text="", icon='REC').enabled = False
        
        # Clear chat
        actions_row.operator("ai_agent.clear_chat", text="", icon='TRASH')
        
        # Export chat
        actions_row.operator("ai_agent.export_chat", text="", icon='EXPORT')
    
    def _draw_message(self, layout, message):
        """Draw a single chat message"""
        # Message container
        msg_row = layout.row()
        
        # Message icon and styling based on type
        if message.type == MessageType.USER:
            msg_row.alert = False
            icon = 'USER'
            prefix = "👤"
        elif message.type == MessageType.ASSISTANT:
            msg_row.alert = False  
            icon = 'OUTLINER_OB_ARMATURE'
            prefix = "🤖"
        elif message.type == MessageType.SYSTEM:
            msg_row.alert = False
            icon = 'INFO'
            prefix = "ℹ️"
        else:  # ERROR
            msg_row.alert = True
            icon = 'ERROR'
            prefix = "❌"
        
        # Message content (truncate if too long)
        content = message.content
        if len(content) > 100:
            content = content[:97] + "..."
        
        # Split into lines for better display
        lines = content.split('\n')
        for i, line in enumerate(lines[:3]):  # Show max 3 lines
            if i == 0:
                msg_row.label(text=f"{prefix} {line}", icon=icon)
            else:
                continuation_row = layout.row()
                continuation_row.label(text=f"    {line}")

class AI_AGENT_PT_suggestions_panel(Panel):
    """Smart suggestions panel"""
    bl_label = "Quick Actions"
    bl_idname = "AI_AGENT_PT_suggestions_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_chat_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        suggestions_system = get_suggestions_system()
        
        # Get context-aware suggestions
        suggestions = suggestions_system.get_suggestions(6)
        
        if not suggestions:
            layout.label(text="Öneriler yükleniyor...", icon='TIME')
            return
        
        # Group suggestions by category
        categories = {}
        for suggestion in suggestions:
            if suggestion.category not in categories:
                categories[suggestion.category] = []
            categories[suggestion.category].append(suggestion)
        
        # Display suggestions by category
        for category, cat_suggestions in categories.items():
            if not cat_suggestions:
                continue
                
            # Category header
            category_display = {
                'create': '🎯 Oluştur',
                'material': '🎨 Materyal', 
                'lighting': '💡 Aydınlatma',
                'animation': '🎬 Animasyon',
                'modify': '🔧 Düzenle',
                'scene': '🏗️ Sahne',
                'render': '📸 Render'
            }.get(category, category.title())
            
            box = layout.box()
            box.label(text=category_display, icon='TOOL_SETTINGS')
            
            # Suggestion buttons
            col = box.column(align=True)
            for suggestion in cat_suggestions[:3]:  # Max 3 per category
                row = col.row()
                
                # Suggestion button
                suggest_op = row.operator("ai_agent.execute_suggestion", 
                                        text=suggestion.text, 
                                        icon=suggestion.icon)
                suggest_op.suggestion_id = suggestion.id
                
                # Info button
                info_op = row.operator("ai_agent.suggestion_info", 
                                     text="", 
                                     icon='INFO')
                info_op.suggestion_id = suggestion.id
                info_op.description = suggestion.description

class AI_AGENT_PT_chat_status_panel(Panel):
    """Chat status and active tasks panel"""
    bl_label = "Status"
    bl_idname = "AI_AGENT_PT_chat_status_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_chat_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        agent_bridge = get_agent_bridge()
        
        # Active tasks
        active_tasks = agent_bridge.get_active_tasks()
        
        if active_tasks:
            box = layout.box()
            box.label(text="🔄 Aktif Görevler", icon='SEQUENCE')
            
            col = box.column(align=True)
            for task in active_tasks[:3]:  # Show max 3 tasks
                row = col.row()
                
                # Task status icon
                status_icon = {
                    'pending': 'TIME',
                    'running': 'PLAY'
                }.get(task.status.value, 'QUESTION')
                
                # Task description (truncated)
                desc = task.description
                if len(desc) > 30:
                    desc = desc[:27] + "..."
                
                row.label(text=desc, icon=status_icon)
                
                # Cancel button
                cancel_op = row.operator("ai_agent.cancel_task", text="", icon='PANEL_CLOSE')
                cancel_op.task_id = task.id
        
        # System status
        status_box = layout.box()
        status_box.label(text="📊 Sistem Durumu", icon='GRAPH')
        
        col = status_box.column(align=True)
        
        # Chat system status
        chat_interface = get_chat_interface()
        if chat_interface.is_initialized:
            col.label(text="✅ Chat sistemi aktif")
        else:
            col.label(text="❌ Chat sistemi pasif")
        
        # Message count
        message_count = len(chat_interface.messages)
        col.label(text=f"💬 {message_count} mesaj")
        
        # Suggestions status
        suggestions_system = get_suggestions_system()
        if suggestions_system.is_initialized:
            suggestion_count = len(suggestions_system.get_suggestions())
            col.label(text=f"💡 {suggestion_count} öneri")

# Property group for chat interface
class ChatProperties(PropertyGroup):
    """Properties for chat interface"""
    
    message_input: StringProperty(
        name="Message",
        description="Type your message here",
        default="",
        maxlen=500
    )
    
    auto_scroll: BoolProperty(
        name="Auto Scroll",
        description="Automatically scroll to latest messages",
        default=True
    )
    
    show_timestamps: BoolProperty(
        name="Show Timestamps",
        description="Show message timestamps",
        default=False
    )
    
    max_messages: StringProperty(
        name="Max Messages",
        description="Maximum number of messages to display",
        default="20"
    )

# Classes to register
classes = [
    ChatProperties,
    AI_AGENT_PT_chat_panel,
    AI_AGENT_PT_suggestions_panel,
    AI_AGENT_PT_chat_status_panel,
]

def register():
    """Register chat panel classes"""
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # Add properties to scene
    bpy.types.Scene.chat_props = bpy.props.PointerProperty(type=ChatProperties)

def unregister():
    """Unregister chat panel classes"""
    # Remove properties
    if hasattr(bpy.types.Scene, 'chat_props'):
        del bpy.types.Scene.chat_props
    
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
