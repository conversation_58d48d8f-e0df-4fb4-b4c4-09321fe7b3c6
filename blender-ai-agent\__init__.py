bl_info = {
    "name": "AI Agent System",
    "author": "inkbytefo",
    "version": (1, 0, 0),
    "blender": (4, 4, 0),
    "location": "View3D > Sidebar > AI Agents",
    "description": "Crew AI powered multi-agent system with MCP integration",
    "warning": "Requires internet connection for AI services",
    "doc_url": "https://github.com/inkbytefo/blender-ai-agent",
    "tracker_url": "https://github.com/inkbytefo/blender-ai-agent/issues",
    "category": "3D View",
    "support": "COMMUNITY"
}

import bpy
import sys
import os
from pathlib import Path

# Add the addon directory to Python path
addon_dir = Path(__file__).parent
if str(addon_dir) not in sys.path:
    sys.path.insert(0, str(addon_dir))

# Import core modules
from . import preferences
from . import operators
from . import ui
from .core import addon_manager
from .crew_system import crew_manager
from .mcp_integration import mcp_client
from .chat_system import chat_interface
from .utils import logging_config

# Initialize modules list for reloading
modules = [
    preferences,
    operators,
    ui,
    addon_manager,
    crew_manager,
    mcp_client,
    chat_interface,
    logging_config
]

# Reload modules for development
if "bpy" in locals():
    import importlib
    for module in modules:
        importlib.reload(module)

def register():
    """Register all classes and properties"""
    try:
        # Setup logging first
        logging_config.setup_logging()

        # Register core modules
        preferences.register()
        operators.register()
        ui.register()

        # Initialize addon manager
        addon_manager.initialize()

        # Initialize crew system
        crew_manager.register()

        # Initialize MCP integration
        mcp_client.register()

        # Initialize chat system
        chat_interface.register()

        # Add custom properties to scene
        bpy.types.Scene.ai_agent_settings = bpy.props.PointerProperty(
            type=preferences.AIAgentSettings
        )

        print("AI Agent System: Successfully registered")

    except Exception as e:
        print(f"AI Agent System: Registration failed - {e}")
        raise

def unregister():
    """Unregister all classes and properties"""
    try:
        # Remove custom properties
        if hasattr(bpy.types.Scene, 'ai_agent_settings'):
            del bpy.types.Scene.ai_agent_settings

        # Cleanup chat system
        chat_interface.unregister()

        # Cleanup MCP integration
        mcp_client.unregister()

        # Cleanup crew system
        crew_manager.unregister()

        # Cleanup addon manager
        addon_manager.cleanup()

        # Unregister modules in reverse order
        ui.unregister()
        operators.unregister()
        preferences.unregister()

        print("AI Agent System: Successfully unregistered")

    except Exception as e:
        print(f"AI Agent System: Unregistration failed - {e}")

if __name__ == "__main__":
    register()
