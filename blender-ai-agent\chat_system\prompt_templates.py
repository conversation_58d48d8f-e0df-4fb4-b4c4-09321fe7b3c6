"""
Turkish-Optimized Prompt Templates for Blender AI Assistant
"""

from typing import Dict, List, Optional, Any
from enum import Enum
import json

class PromptType(Enum):
    """Types of prompts for different scenarios"""
    SYSTEM_BASE = "system_base"
    SYSTEM_EXPERT = "system_expert"
    CONTEXT_BLENDER = "context_blender"
    INTENT_DETECTION = "intent_detection"
    ENTITY_EXTRACTION = "entity_extraction"
    TASK_PLANNING = "task_planning"
    ERROR_HANDLING = "error_handling"

class TurkishPromptTemplates:
    """Turkish-optimized prompt templates for Blender AI"""
    
    @staticmethod
    def get_system_prompt(prompt_type: PromptType = PromptType.SYSTEM_BASE) -> str:
        """Get system prompt based on type"""
        
        if prompt_type == PromptType.SYSTEM_BASE:
            return """Sen Blender 3D yazılımı için geliştirilmiş Türkçe konuşan bir AI asistanısın. 
Kullanıcıların doğal dil komutlarını analiz edip Blender işlemlerine dönüştürüyorsun.

KİŞİLİĞİN:
- Yardımsever ve sabırlı
- Teknik konularda uzman ama anlaşılır
- Türkçe dilbilgisi kurallarına uygun
- Yaratıcı ve çözüm odaklı

YETENEKLER:
✅ Obje oluşturma (küp, küre, karakter, bina vb.)
✅ Obje düzenleme (boyut, konum, rotasyon, materyal)
✅ Animasyon oluşturma (hareket, keyframe, timeline)
✅ Materyal ve tekstür işlemleri
✅ Aydınlatma ve render ayarları
✅ Sahne yönetimi ve organizasyon
✅ Türkçe doğal dil anlama
✅ Çok adımlı görev planlama

TÜRKÇE DİL KURALLARI:
- Türkçe obje isimlerini İngilizce Blender terimlerine çevir
- Kültürel bağlamı dikkate al (örn: "ev" → house/building)
- Türkçe sayıları ve ölçü birimlerini tanı
- Günlük konuşma dilini anlayabilir

YANIT FORMATI:
Sadece geçerli JSON formatında yanıt ver. Ek açıklama yapma."""

        elif prompt_type == PromptType.SYSTEM_EXPERT:
            return """Sen Blender 3D konusunda uzman, Türkçe konuşan bir AI asistanısın.
Karmaşık 3D modelleme, animasyon ve render işlemlerinde rehberlik ediyorsun.

UZMANLIK ALANLARI:
🎯 İleri seviye modelleme teknikleri
🎯 Karmaşık animasyon sistemleri
🎯 PBR materyal oluşturma
🎯 Procedural texture ve shader'lar
🎯 Lighting ve cinematography
🎯 Render optimization
🎯 Python scripting ve addon geliştirme

YAKLAŞIM:
- Teknik detayları açıkla ama anlaşılır tut
- Alternatif yöntemler öner
- Best practice'leri paylaş
- Performans optimizasyonu öner

Sadece JSON formatında yanıt ver."""

        return ""
    
    @staticmethod
    def get_context_template() -> str:
        """Get Blender context template"""
        return """BLENDER CONTEXT:
- Sahne: {scene_name}
- Mod: {mode}
- Seçili Objeler: {selected_objects}
- Aktif Obje: {active_object}
- Toplam Obje: {total_objects}
- Frame: {current_frame}/{frame_end}
- Render Engine: {render_engine}

WORKSPACE: {workspace}
COLLECTION: {active_collection}"""

    @staticmethod
    def get_intent_examples() -> Dict[str, List[str]]:
        """Get Turkish intent examples for few-shot learning"""
        return {
            "create_object": [
                "Bir küp oluştur",
                "Kırmızı bir küre yap",
                "Sahneye bir masa ekle",
                "Karakter modeli oluştur",
                "Modern bir ev tasarla"
            ],
            "modify_object": [
                "Küpü 2 kat büyüt",
                "Objeyi sağa taşı",
                "45 derece döndür",
                "Rengini maviye çevir",
                "Boyutunu yarıya indir"
            ],
            "animate": [
                "Küpü hareket ettir",
                "Dönme animasyonu ekle",
                "Karakteri yürüt",
                "Kamera hareketi oluştur",
                "10 saniye animasyon yap"
            ],
            "material": [
                "Metal materyal ekle",
                "Parlak yüzey oluştur",
                "Ahşap tekstür uygula",
                "Cam materyal yap",
                "PBR materyal oluştur"
            ],
            "lighting": [
                "Sahneyi aydınlat",
                "Güneş ışığı ekle",
                "Spot ışık koy",
                "Gölgeleri ayarla",
                "Dramatic lighting oluştur"
            ],
            "render": [
                "Render al",
                "Yüksek kalite çıktı",
                "PNG olarak kaydet",
                "4K render oluştur",
                "Animasyon render et"
            ],
            "scene_management": [
                "Sahneyi temizle",
                "Objeleri grupla",
                "Collection oluştur",
                "Sahne bilgisi ver",
                "Backup al"
            ],
            "help": [
                "Nasıl yapılır?",
                "Yardım et",
                "Öğret",
                "Açıkla",
                "Rehberlik et"
            ]
        }
    
    @staticmethod
    def get_entity_patterns() -> Dict[str, Dict[str, List[str]]]:
        """Get Turkish entity patterns"""
        return {
            "objects": {
                "primitives": ["küp", "küre", "silindir", "koni", "düzlem", "torus"],
                "characters": ["karakter", "insan", "robot", "yaratık", "hayvan"],
                "buildings": ["ev", "bina", "kule", "köprü", "duvar", "çatı"],
                "furniture": ["masa", "sandalye", "yatak", "dolap", "raf"],
                "vehicles": ["araba", "uçak", "gemi", "bisiklet", "motor"]
            },
            "materials": {
                "metals": ["metal", "çelik", "demir", "altın", "gümüş", "bakır"],
                "organics": ["ahşap", "deri", "kumaş", "kağıt", "plastik"],
                "stones": ["taş", "mermer", "granit", "beton", "tuğla"],
                "glass": ["cam", "kristal", "şeffaf"]
            },
            "colors": {
                "basic": ["kırmızı", "mavi", "yeşil", "sarı", "mor", "turuncu", "pembe", "siyah", "beyaz", "gri"],
                "advanced": ["lacivert", "bordo", "turkuaz", "lime", "magenta", "altın", "gümüş"]
            },
            "actions": {
                "create": ["oluştur", "yap", "üret", "kur", "inşa et", "yarat", "ekle"],
                "modify": ["değiştir", "düzenle", "ayarla", "güncelle", "büyüt", "küçült"],
                "move": ["taşı", "götür", "yerleştir", "konumlandır"],
                "rotate": ["döndür", "çevir", "eğ"],
                "scale": ["büyüt", "küçült", "ölçekle", "boyutlandır"]
            },
            "numbers": {
                "turkish": ["bir", "iki", "üç", "dört", "beş", "altı", "yedi", "sekiz", "dokuz", "on"],
                "units": ["metre", "cm", "mm", "birim", "derece", "saniye", "dakika"]
            }
        }
    
    @staticmethod
    def get_response_format_schema() -> Dict[str, Any]:
        """Get structured response format schema"""
        return {
            "intent": "string (create_object|modify_object|animate|material|lighting|render|scene_management|help|conversation|unknown)",
            "confidence": "float (0.0-1.0)",
            "entities": {
                "objects": "list of detected object names",
                "materials": "list of detected material types",
                "colors": "list of detected colors",
                "numbers": "list of detected numbers/quantities",
                "actions": "list of detected action verbs",
                "properties": "list of detected properties"
            },
            "parameters": {
                "action": "primary action to perform",
                "target": "target object or element",
                "values": "numeric values and units",
                "options": "additional options and settings"
            },
            "reasoning": "explanation of analysis in Turkish",
            "suggested_actions": "list of specific Blender operations",
            "response_text": "natural Turkish response to user",
            "complexity": "simple|medium|complex",
            "requires_confirmation": "boolean for destructive operations"
        }
    
    @staticmethod
    def get_task_planning_template() -> str:
        """Get task planning prompt template"""
        return """GÖREV PLANLAMA:

Kullanıcı isteği: {user_request}
Mevcut durum: {current_state}

ADIM ADIM PLAN:
1. Analiz: İsteği detaylı analiz et
2. Gereksinimler: Hangi Blender araçları gerekli?
3. Sıralama: İşlemlerin doğru sırası
4. Kontrol: Her adımda kontrol noktaları
5. Sonuç: Beklenen çıktı

PLAN FORMATI:
```json
{
  "steps": [
    {
      "step_number": 1,
      "description": "Adım açıklaması",
      "blender_operation": "Blender komutu",
      "parameters": {},
      "expected_result": "Beklenen sonuç",
      "validation": "Kontrol yöntemi"
    }
  ],
  "estimated_time": "Tahmini süre",
  "difficulty": "Zorluk seviyesi",
  "prerequisites": "Ön gereksinimler"
}
```"""
    
    @staticmethod
    def get_error_handling_template() -> str:
        """Get error handling prompt template"""
        return """HATA YÖNETİMİ:

Hata durumu: {error_type}
Hata mesajı: {error_message}
Kullanıcı isteği: {original_request}

ÇÖZÜM YAKLAŞIMI:
1. Hatayı anla ve kategorize et
2. Alternatif çözümler öner
3. Kullanıcıya açık geri bildirim ver
4. Gerekirse adım adım rehberlik et

YANIT FORMATI:
```json
{
  "error_analysis": "Hatanın açıklaması",
  "alternative_solutions": ["Çözüm 1", "Çözüm 2"],
  "user_message": "Kullanıcıya açıklama",
  "recovery_steps": ["Adım 1", "Adım 2"],
  "prevention_tips": "Gelecekte nasıl önlenir"
}
```"""
    
    @staticmethod
    def format_prompt(template: str, **kwargs) -> str:
        """Format prompt template with variables"""
        try:
            return template.format(**kwargs)
        except KeyError as e:
            # Handle missing variables gracefully
            return template.replace(f"{{{e.args[0]}}}", f"[{e.args[0]} not provided]")
    
    @staticmethod
    def get_few_shot_examples(intent: str, count: int = 3) -> List[Dict[str, str]]:
        """Get few-shot examples for specific intent"""
        examples = TurkishPromptTemplates.get_intent_examples()
        
        if intent not in examples:
            return []
        
        intent_examples = examples[intent][:count]
        
        # Create example conversations
        few_shot = []
        for example in intent_examples:
            few_shot.append({
                "user": example,
                "assistant": f"Intent: {intent}, Confidence: 0.9, Processing: {example}"
            })
        
        return few_shot
    
    @staticmethod
    def create_contextual_prompt(
        user_message: str,
        blender_context: Dict[str, Any],
        conversation_history: List[Dict[str, str]] = None,
        intent_hint: Optional[str] = None
    ) -> str:
        """Create contextual prompt with all information"""
        
        prompt_parts = []
        
        # Add context
        context_template = TurkishPromptTemplates.get_context_template()
        formatted_context = TurkishPromptTemplates.format_prompt(
            context_template, **blender_context
        )
        prompt_parts.append(formatted_context)
        prompt_parts.append("")
        
        # Add conversation history
        if conversation_history:
            prompt_parts.append("SON KONUŞMA:")
            for msg in conversation_history[-3:]:  # Last 3 messages
                role = "Kullanıcı" if msg["role"] == "user" else "Asistan"
                prompt_parts.append(f"{role}: {msg['content']}")
            prompt_parts.append("")
        
        # Add few-shot examples if intent hint provided
        if intent_hint:
            examples = TurkishPromptTemplates.get_few_shot_examples(intent_hint, 2)
            if examples:
                prompt_parts.append("ÖRNEK KONUŞMALAR:")
                for example in examples:
                    prompt_parts.append(f"Kullanıcı: {example['user']}")
                    prompt_parts.append(f"Asistan: {example['assistant']}")
                prompt_parts.append("")
        
        # Add current message
        prompt_parts.append(f"KULLANICI MESAJI: {user_message}")
        prompt_parts.append("")
        
        # Add response format
        schema = TurkishPromptTemplates.get_response_format_schema()
        prompt_parts.append("YANIT FORMATI:")
        prompt_parts.append(json.dumps(schema, indent=2, ensure_ascii=False))
        
        return "\n".join(prompt_parts)
