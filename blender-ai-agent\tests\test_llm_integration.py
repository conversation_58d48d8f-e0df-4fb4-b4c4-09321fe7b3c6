"""
Test LLM Integration - Test the new LLM-based NLP processor
"""

import asyncio
import unittest
from unittest.mock import Mock, patch, AsyncMock
import sys
import os

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm_integration import LLMConfig, LLMProviderType, OpenAIProvider
from chat_system.llm_nlp_processor import LLMNLPProcessor, Intent
from config.llm_config import LLMConfigManager, LLMPreset

class TestLLMIntegration(unittest.TestCase):
    """Test LLM integration components"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_config = LLMConfig(
            provider_type=LLMProviderType.OPENAI,
            model="gpt-4o-mini",
            api_key="test_key",
            temperature=0.7,
            max_tokens=1000
        )
    
    def test_llm_config_creation(self):
        """Test LLM configuration creation"""
        config = LLMConfig(
            provider_type=LLMProviderType.OPENAI,
            model="gpt-4o",
            api_key="test_key"
        )
        
        self.assertEqual(config.provider_type, LLMProviderType.OPENAI)
        self.assertEqual(config.model, "gpt-4o")
        self.assertEqual(config.api_key, "test_key")
        self.assertEqual(config.temperature, 0.7)  # default
        self.assertEqual(config.max_tokens, 2000)  # default
    
    def test_openai_provider_validation(self):
        """Test OpenAI provider configuration validation"""
        provider = OpenAIProvider(self.test_config)
        
        # Valid config
        self.assertTrue(provider.validate_config())
        
        # Invalid config (no API key)
        invalid_config = LLMConfig(
            provider_type=LLMProviderType.OPENAI,
            model="gpt-4o",
            api_key=None
        )
        invalid_provider = OpenAIProvider(invalid_config)
        self.assertFalse(invalid_provider.validate_config())
    
    def test_openai_provider_models(self):
        """Test OpenAI provider available models"""
        provider = OpenAIProvider(self.test_config)
        models = provider.get_available_models()
        
        self.assertIn("gpt-4o", models)
        self.assertIn("gpt-4o-mini", models)
        self.assertIn("gpt-3.5-turbo", models)
    
    def test_cost_estimation(self):
        """Test cost estimation"""
        provider = OpenAIProvider(self.test_config)
        
        # Test with known model
        cost = provider.estimate_cost(1000, 500)  # 1000 input, 500 output tokens
        self.assertGreater(cost, 0)
        
        # Test with unknown model
        unknown_config = LLMConfig(
            provider_type=LLMProviderType.OPENAI,
            model="unknown-model",
            api_key="test_key"
        )
        unknown_provider = OpenAIProvider(unknown_config)
        cost = unknown_provider.estimate_cost(1000, 500)
        self.assertEqual(cost, 0.0)
    
    @patch('aiohttp.ClientSession')
    async def test_openai_provider_initialization(self, mock_session):
        """Test OpenAI provider initialization"""
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
        
        provider = OpenAIProvider(self.test_config)
        result = await provider.initialize()
        
        self.assertTrue(result)
        self.assertTrue(provider.is_initialized)
    
    def test_config_manager_presets(self):
        """Test configuration manager presets"""
        config_manager = LLMConfigManager()
        presets = config_manager.get_presets()
        
        self.assertIn("fast_general", presets)
        self.assertIn("balanced", presets)
        self.assertIn("creative", presets)
        self.assertIn("technical", presets)
        
        # Test preset properties
        balanced = presets["balanced"]
        self.assertEqual(balanced.provider_type, LLMProviderType.OPENAI)
        self.assertEqual(balanced.use_case, "general")
    
    def test_config_manager_validation(self):
        """Test configuration validation"""
        config_manager = LLMConfigManager()
        
        # Valid config
        valid_config = LLMConfig(
            provider_type=LLMProviderType.OPENAI,
            model="gpt-4o-mini",
            api_key="test_key"
        )
        is_valid, message = config_manager.validate_config(valid_config)
        self.assertTrue(is_valid)
        
        # Invalid config (no API key)
        invalid_config = LLMConfig(
            provider_type=LLMProviderType.OPENAI,
            model="gpt-4o-mini",
            api_key=None
        )
        is_valid, message = config_manager.validate_config(invalid_config)
        self.assertFalse(is_valid)
        self.assertIn("API key", message)

class TestLLMNLPProcessor(unittest.TestCase):
    """Test LLM-based NLP processor"""
    
    def setUp(self):
        """Set up test environment"""
        self.test_config = LLMConfig(
            provider_type=LLMProviderType.OPENAI,
            model="gpt-4o-mini",
            api_key="test_key",
            temperature=0.7,
            max_tokens=1000
        )
        self.processor = LLMNLPProcessor(self.test_config)
    
    @patch('llm_integration.get_llm_provider')
    async def test_processor_initialization(self, mock_get_provider):
        """Test LLM NLP processor initialization"""
        # Mock provider
        mock_provider = AsyncMock()
        mock_provider.initialize.return_value = True
        mock_get_provider.return_value = mock_provider
        
        result = await self.processor.initialize()
        
        self.assertTrue(result)
        self.assertTrue(self.processor.is_initialized)
        self.assertIsNotNone(self.processor.llm_provider)
    
    def test_system_prompt_creation(self):
        """Test system prompt creation"""
        system_prompt = self.processor._create_system_prompt()
        
        self.assertIn("Blender", system_prompt)
        self.assertIn("Türkçe", system_prompt)
        self.assertIn("JSON", system_prompt)
        self.assertIn("create_object", system_prompt)
        self.assertIn("modify_object", system_prompt)
    
    def test_context_conversion(self):
        """Test conversation context to prompt conversion"""
        from chat_system.llm_nlp_processor import ConversationContext
        
        context = ConversationContext(
            blender_context={
                "scene_name": "Test Scene",
                "mode": "EDIT",
                "selected_objects": ["Cube", "Sphere"],
                "active_object": "Cube",
                "total_objects": 5
            },
            conversation_history=[
                {"role": "user", "content": "Bir küp oluştur"},
                {"role": "assistant", "content": "Küp oluşturuldu"}
            ],
            user_preferences={"language": "turkish"}
        )
        
        prompt_context = context.to_prompt_context()
        
        self.assertIn("Test Scene", prompt_context)
        self.assertIn("EDIT", prompt_context)
        self.assertIn("Cube", prompt_context)
        self.assertIn("Sphere", prompt_context)
    
    def test_fallback_response(self):
        """Test fallback response creation"""
        fallback = self.processor._create_fallback_response("test message", "test error")
        
        self.assertEqual(fallback.original_text, "test message")
        self.assertEqual(fallback.intent, Intent.UNKNOWN)
        self.assertEqual(fallback.confidence, 0.0)
        self.assertIn("test error", fallback.reasoning)
    
    @patch('llm_integration.get_llm_provider')
    async def test_message_parsing_with_mock_llm(self, mock_get_provider):
        """Test message parsing with mocked LLM response"""
        # Mock LLM provider and response
        mock_provider = AsyncMock()
        mock_provider.initialize.return_value = True
        
        mock_llm_response = Mock()
        mock_llm_response.success = True
        mock_llm_response.content = '''
        {
            "intent": "create_object",
            "confidence": 0.9,
            "entities": {
                "objects": ["küp"],
                "colors": ["kırmızı"]
            },
            "parameters": {
                "action": "create",
                "target": "cube"
            },
            "reasoning": "Kullanıcı kırmızı bir küp oluşturmak istiyor",
            "suggested_actions": ["Create cube", "Apply red material"],
            "response_text": "Kırmızı küp oluşturuyorum!"
        }
        '''
        mock_llm_response.parse_json.return_value = {
            "intent": "create_object",
            "confidence": 0.9,
            "entities": {
                "objects": ["küp"],
                "colors": ["kırmızı"]
            },
            "parameters": {
                "action": "create",
                "target": "cube"
            },
            "reasoning": "Kullanıcı kırmızı bir küp oluşturmak istiyor",
            "suggested_actions": ["Create cube", "Apply red material"],
            "response_text": "Kırmızı küp oluşturuyorum!"
        }
        
        mock_provider.generate_structured_response.return_value = mock_llm_response
        mock_get_provider.return_value = mock_provider
        
        # Initialize processor
        await self.processor.initialize()
        
        # Test message parsing
        parsed = await self.processor.parse_message("Kırmızı bir küp oluştur")
        
        self.assertEqual(parsed.intent, Intent.CREATE_OBJECT)
        self.assertEqual(parsed.confidence, 0.9)
        self.assertIn("küp", parsed.entities.get("objects", []))
        self.assertIn("kırmızı", parsed.entities.get("colors", []))
        self.assertEqual(parsed.parameters.get("action"), "create")

class TestTurkishLanguageSupport(unittest.TestCase):
    """Test Turkish language support"""
    
    def test_turkish_intent_examples(self):
        """Test Turkish intent examples"""
        from chat_system.prompt_templates import TurkishPromptTemplates
        
        examples = TurkishPromptTemplates.get_intent_examples()
        
        # Test create_object examples
        create_examples = examples["create_object"]
        self.assertIn("Bir küp oluştur", create_examples)
        self.assertIn("Kırmızı bir küre yap", create_examples)
        
        # Test modify_object examples
        modify_examples = examples["modify_object"]
        self.assertIn("Küpü 2 kat büyüt", modify_examples)
        self.assertIn("45 derece döndür", modify_examples)
    
    def test_turkish_entity_patterns(self):
        """Test Turkish entity patterns"""
        from chat_system.prompt_templates import TurkishPromptTemplates
        
        patterns = TurkishPromptTemplates.get_entity_patterns()
        
        # Test object patterns
        objects = patterns["objects"]
        self.assertIn("küp", objects["primitives"])
        self.assertIn("küre", objects["primitives"])
        self.assertIn("ev", objects["buildings"])
        
        # Test color patterns
        colors = patterns["colors"]
        self.assertIn("kırmızı", colors["basic"])
        self.assertIn("mavi", colors["basic"])
    
    def test_response_format_schema(self):
        """Test response format schema"""
        from chat_system.prompt_templates import TurkishPromptTemplates
        
        schema = TurkishPromptTemplates.get_response_format_schema()
        
        self.assertIn("intent", schema)
        self.assertIn("confidence", schema)
        self.assertIn("entities", schema)
        self.assertIn("parameters", schema)
        self.assertIn("reasoning", schema)
        self.assertIn("response_text", schema)

def run_async_test(coro):
    """Helper to run async tests"""
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()

if __name__ == '__main__':
    # Run tests
    unittest.main(verbosity=2)
