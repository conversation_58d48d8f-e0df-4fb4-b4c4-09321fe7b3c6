"""
LLM Provider Factory - Creates and manages LLM provider instances
"""

from typing import Dict, List, Optional, Type
from .llm_provider import <PERSON><PERSON><PERSON><PERSON>, LLMConfig, LLMProviderType
from .openai_provider import OpenAIProvider
from ..utils.logging_config import get_logger

logger = get_logger("provider_factory")

# Registry of available providers
PROVIDER_REGISTRY: Dict[LLMProviderType, Type[LLMProvider]] = {
    LLMProviderType.OPENAI: OpenAIProvider,
    # Future providers will be added here
    # LLMProviderType.ANTHROPIC: AnthropicProvider,
    # LLMProviderType.OLLAMA: OllamaProvider,
    # LLMProviderType.AZURE_OPENAI: AzureOpenAIProvider,
    # LLMProviderType.GOOGLE_GEMINI: GoogleGeminiProvider,
}

def get_llm_provider(config: LLMConfig) -> Optional[LLMProvider]:
    """
    Create and return an LLM provider instance based on configuration
    
    Args:
        config: LLM configuration
        
    Returns:
        LLM provider instance or None if provider not supported
    """
    try:
        provider_class = PROVIDER_REGISTRY.get(config.provider_type)
        
        if provider_class is None:
            logger.error(f"Unsupported provider type: {config.provider_type}")
            return None
        
        provider = provider_class(config)
        logger.info(f"Created {config.provider_type.value} provider with model {config.model}")
        
        return provider
        
    except Exception as e:
        logger.error(f"Failed to create LLM provider: {e}")
        return None

def list_available_providers() -> List[LLMProviderType]:
    """
    Get list of available LLM provider types
    
    Returns:
        List of supported provider types
    """
    return list(PROVIDER_REGISTRY.keys())

def get_provider_info() -> Dict[str, Dict[str, any]]:
    """
    Get information about available providers
    
    Returns:
        Dictionary with provider information
    """
    provider_info = {}
    
    for provider_type in PROVIDER_REGISTRY.keys():
        # Create a temporary instance to get model info
        try:
            temp_config = LLMConfig(
                provider_type=provider_type,
                model="temp",  # Temporary model name
                api_key="temp"  # Temporary API key
            )
            
            provider_class = PROVIDER_REGISTRY[provider_type]
            temp_provider = provider_class(temp_config)
            
            provider_info[provider_type.value] = {
                "name": provider_type.value.replace("_", " ").title(),
                "available_models": temp_provider.get_available_models(),
                "requires_api_key": True,  # Most providers require API keys
                "supports_structured_output": True,  # Most modern providers support this
            }
            
        except Exception as e:
            logger.warning(f"Could not get info for provider {provider_type}: {e}")
            provider_info[provider_type.value] = {
                "name": provider_type.value.replace("_", " ").title(),
                "available_models": [],
                "requires_api_key": True,
                "supports_structured_output": True,
            }
    
    return provider_info

def validate_provider_config(config: LLMConfig) -> bool:
    """
    Validate provider configuration
    
    Args:
        config: LLM configuration to validate
        
    Returns:
        True if configuration is valid, False otherwise
    """
    try:
        if config.provider_type not in PROVIDER_REGISTRY:
            logger.error(f"Unsupported provider type: {config.provider_type}")
            return False
        
        provider_class = PROVIDER_REGISTRY[config.provider_type]
        temp_provider = provider_class(config)
        
        return temp_provider.validate_config()
        
    except Exception as e:
        logger.error(f"Config validation failed: {e}")
        return False

def create_default_config(provider_type: LLMProviderType) -> LLMConfig:
    """
    Create default configuration for a provider type
    
    Args:
        provider_type: Type of LLM provider
        
    Returns:
        Default configuration for the provider
    """
    default_configs = {
        LLMProviderType.OPENAI: LLMConfig(
            provider_type=LLMProviderType.OPENAI,
            model="gpt-4o-mini",
            temperature=0.7,
            max_tokens=2000,
            timeout=30
        ),
        # Future default configs will be added here
    }
    
    return default_configs.get(provider_type, LLMConfig(
        provider_type=provider_type,
        model="default",
        temperature=0.7,
        max_tokens=2000,
        timeout=30
    ))

# Global provider instance cache
_provider_cache: Dict[str, LLMProvider] = {}

def get_cached_provider(config: LLMConfig) -> Optional[LLMProvider]:
    """
    Get cached provider instance or create new one
    
    Args:
        config: LLM configuration
        
    Returns:
        Cached or new provider instance
    """
    cache_key = f"{config.provider_type.value}_{config.model}_{config.api_key[:8] if config.api_key else 'none'}"
    
    if cache_key in _provider_cache:
        cached_provider = _provider_cache[cache_key]
        if cached_provider.is_initialized:
            return cached_provider
        else:
            # Remove invalid cached provider
            del _provider_cache[cache_key]
    
    # Create new provider
    provider = get_llm_provider(config)
    if provider:
        _provider_cache[cache_key] = provider
    
    return provider

def clear_provider_cache():
    """Clear all cached provider instances"""
    global _provider_cache
    
    # Cleanup all cached providers
    for provider in _provider_cache.values():
        try:
            provider.cleanup()
        except Exception as e:
            logger.warning(f"Error cleaning up cached provider: {e}")
    
    _provider_cache.clear()
    logger.info("Provider cache cleared")
