"""
Logging Configuration - Setup logging for the AI Agent System
"""

import logging
import sys
from pathlib import Path
from datetime import datetime

def setup_logging(level=logging.INFO):
    """Setup logging configuration for the addon"""
    
    # Get addon directory
    addon_dir = Path(__file__).parent.parent
    logs_dir = addon_dir / "logs"
    logs_dir.mkdir(exist_ok=True)
    
    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = logs_dir / f"ai_agent_{timestamp}.log"
    
    # Configure root logger
    logger = logging.getLogger()
    logger.setLevel(level)
    
    # Clear existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler (for Blender console)
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # File handler
    try:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        print(f"Failed to setup file logging: {e}")
    
    # Log startup message
    logger.info("AI Agent System logging initialized")
    logger.info(f"Log file: {log_file}")
    
    return logger

def get_logger(name):
    """Get a logger instance for a specific module"""
    return logging.getLogger(f"ai_agent.{name}")

def cleanup_old_logs(days=7):
    """Clean up log files older than specified days"""
    addon_dir = Path(__file__).parent.parent
    logs_dir = addon_dir / "logs"
    
    if not logs_dir.exists():
        return
    
    cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
    
    for log_file in logs_dir.glob("ai_agent_*.log"):
        try:
            if log_file.stat().st_mtime < cutoff_time:
                log_file.unlink()
                print(f"Deleted old log file: {log_file}")
        except Exception as e:
            print(f"Failed to delete log file {log_file}: {e}")
