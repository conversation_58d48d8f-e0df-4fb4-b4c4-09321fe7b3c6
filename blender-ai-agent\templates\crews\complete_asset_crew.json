{"id": "complete_asset_crew", "name": "Complete Asset Pipeline Crew", "description": "Full pipeline crew for complete asset creation from modeling to rendering", "agents": [{"role": "3D Modeling Specialist", "goal": "Create high-quality 3D models based on specifications and requirements", "backstory": "You are an expert 3D artist with 10+ years of experience in creating detailed and optimized 3D models. You understand topology, edge flow, and optimization techniques for different use cases including games, animation, and architectural visualization.", "tools": ["mesh_creator", "modifier_tool", "topology_optimizer", "uv_mapper"], "verbose": true, "allow_delegation": false}, {"role": "Material and Texture Artist", "goal": "Create realistic and stylized materials and textures for 3D objects", "backstory": "You are a skilled material artist who understands PBR workflows, shader networks, and texture painting. You can create both photorealistic and stylized materials that enhance the visual quality of 3D scenes.", "tools": ["material_creator", "texture_painter", "node_editor", "image_processor"], "verbose": true, "allow_delegation": false}, {"role": "Lighting Artist", "goal": "Create compelling lighting setups that enhance mood and visual storytelling", "backstory": "You are a lighting specialist who understands color theory, mood creation, and technical aspects of 3D lighting. You can create both realistic and artistic lighting setups for various scenarios.", "tools": ["light_manager", "hdri_loader", "shadow_controller", "color_grader"], "verbose": true, "allow_delegation": false}, {"role": "Render Specialist", "goal": "Optimize render settings and produce high-quality final images", "backstory": "You are a render expert who understands different render engines, optimization techniques, and post-processing workflows. You can balance quality and render times to achieve the best results.", "tools": ["render_optimizer", "compositor", "denoiser", "batch_renderer"], "verbose": true, "allow_delegation": false}], "tasks": [{"description": "Create the base 3D model according to specifications with proper topology and UV mapping", "agent_role": "3D Modeling Specialist", "expected_output": "A complete 3D model with proper topology and UV coordinates ready for texturing"}, {"description": "Create realistic materials and textures for the model using PBR workflow", "agent_role": "Material and Texture Artist", "expected_output": "Realistic materials and textures applied to the model with proper shader setup"}, {"description": "Set up professional lighting for the scene to enhance the model presentation", "agent_role": "Lighting Artist", "expected_output": "Professional lighting setup that enhances the model and creates the desired mood"}, {"description": "Render the final images with optimal settings and post-processing", "agent_role": "Render Specialist", "expected_output": "High-quality rendered images of the asset with proper composition and post-processing"}], "process": "sequential", "verbose": true}