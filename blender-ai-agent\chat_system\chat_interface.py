"""
Chat Interface - Modern chat UI component for natural language interaction
"""

import bpy
import time
from typing import List, Dict, Optional, Any
from dataclasses import dataclass
from enum import Enum

from ..utils.logging_config import get_logger

logger = get_logger("chat_interface")

class MessageType(Enum):
    """Types of chat messages"""
    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"
    ERROR = "error"

@dataclass
class ChatMessage:
    """Chat message data structure"""
    id: str
    type: MessageType
    content: str
    timestamp: float
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

class ChatInterface:
    """Modern chat interface for AI interaction"""
    
    def __init__(self):
        self.messages: List[ChatMessage] = []
        self.current_input = ""
        self.is_typing = False
        self.typing_start_time = 0.0
        self.max_messages = 100
        self.is_initialized = False
        
        # Callbacks
        self.on_message_sent: Optional[callable] = None
        self.on_message_received: Optional[callable] = None
        
    def initialize(self):
        """Initialize chat interface"""
        try:
            # Add welcome message
            self.add_system_message(
                "🤖 Merhaba! Ben AI Assistant'ınızım. Size Blender'da nasıl yardım edebilirim?\n\n"
                "💡 Örnekler:\n"
                "• 'Bir küp oluştur'\n"
                "• 'Bu obje için materyal yap'\n"
                "• 'Karakterimi animasyonlu yap'\n"
                "• 'Sahneyi aydınlat'"
            )
            
            self.is_initialized = True
            logger.info("Chat interface initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize chat interface: {e}")
            raise
    
    def add_message(self, message_type: MessageType, content: str, metadata: Dict = None) -> str:
        """Add a new message to chat"""
        try:
            message_id = f"msg_{int(time.time() * 1000)}"
            
            message = ChatMessage(
                id=message_id,
                type=message_type,
                content=content,
                timestamp=time.time(),
                metadata=metadata or {}
            )
            
            self.messages.append(message)
            
            # Limit message history
            if len(self.messages) > self.max_messages:
                self.messages = self.messages[-self.max_messages:]
            
            # Trigger callback
            if message_type == MessageType.USER and self.on_message_sent:
                self.on_message_sent(message)
            elif message_type == MessageType.ASSISTANT and self.on_message_received:
                self.on_message_received(message)
            
            logger.debug(f"Added {message_type.value} message: {content[:50]}...")
            return message_id
            
        except Exception as e:
            logger.error(f"Failed to add message: {e}")
            return ""
    
    def add_user_message(self, content: str, metadata: Dict = None) -> str:
        """Add user message"""
        return self.add_message(MessageType.USER, content, metadata)
    
    def add_assistant_message(self, content: str, metadata: Dict = None) -> str:
        """Add assistant message"""
        return self.add_message(MessageType.ASSISTANT, content, metadata)
    
    def add_system_message(self, content: str, metadata: Dict = None) -> str:
        """Add system message"""
        return self.add_message(MessageType.SYSTEM, content, metadata)
    
    def add_error_message(self, content: str, metadata: Dict = None) -> str:
        """Add error message"""
        return self.add_message(MessageType.ERROR, content, metadata)
    
    def start_typing(self):
        """Start typing indicator"""
        self.is_typing = True
        self.typing_start_time = time.time()
    
    def stop_typing(self):
        """Stop typing indicator"""
        self.is_typing = False
        self.typing_start_time = 0.0
    
    def get_typing_duration(self) -> float:
        """Get current typing duration"""
        if self.is_typing:
            return time.time() - self.typing_start_time
        return 0.0
    
    def clear_messages(self):
        """Clear all messages"""
        self.messages.clear()
        logger.info("Chat messages cleared")
    
    def get_recent_messages(self, count: int = 10) -> List[ChatMessage]:
        """Get recent messages"""
        return self.messages[-count:] if self.messages else []
    
    def get_conversation_context(self, max_messages: int = 5) -> str:
        """Get conversation context as string"""
        recent_messages = self.get_recent_messages(max_messages)
        context_lines = []
        
        for msg in recent_messages:
            if msg.type == MessageType.USER:
                context_lines.append(f"Kullanıcı: {msg.content}")
            elif msg.type == MessageType.ASSISTANT:
                context_lines.append(f"Asistan: {msg.content}")
        
        return "\n".join(context_lines)
    
    def export_conversation(self) -> Dict[str, Any]:
        """Export conversation to dictionary"""
        return {
            'messages': [
                {
                    'id': msg.id,
                    'type': msg.type.value,
                    'content': msg.content,
                    'timestamp': msg.timestamp,
                    'metadata': msg.metadata
                }
                for msg in self.messages
            ],
            'exported_at': time.time()
        }
    
    def import_conversation(self, data: Dict[str, Any]):
        """Import conversation from dictionary"""
        try:
            self.clear_messages()
            
            for msg_data in data.get('messages', []):
                message = ChatMessage(
                    id=msg_data['id'],
                    type=MessageType(msg_data['type']),
                    content=msg_data['content'],
                    timestamp=msg_data['timestamp'],
                    metadata=msg_data.get('metadata', {})
                )
                self.messages.append(message)
            
            logger.info(f"Imported {len(self.messages)} messages")
            
        except Exception as e:
            logger.error(f"Failed to import conversation: {e}")
    
    def cleanup(self):
        """Cleanup chat interface"""
        self.clear_messages()
        self.current_input = ""
        self.stop_typing()
        self.is_initialized = False
        logger.info("Chat interface cleaned up")

# Global chat interface instance
_chat_interface = None

def get_chat_interface():
    """Get the chat interface instance"""
    global _chat_interface
    if _chat_interface is None:
        _chat_interface = ChatInterface()
    return _chat_interface
