"""
LLM-based Natural Language Processor - Advanced Turkish intent recognition and response generation
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import time

from ..llm_integration import <PERSON>MProvider, LLMResponse, LLMConfig, get_llm_provider
from ..utils.logging_config import get_logger

logger = get_logger("llm_nlp_processor")

class Intent(Enum):
    """User intent categories"""
    CREATE_OBJECT = "create_object"
    MODIFY_OBJECT = "modify_object"
    ANIMATE = "animate"
    MATERIAL = "material"
    LIGHTING = "lighting"
    RENDER = "render"
    SCENE_MANAGEMENT = "scene_management"
    HELP = "help"
    CONVERSATION = "conversation"
    UNKNOWN = "unknown"

@dataclass
class ParsedMessage:
    """Parsed user message with intent and parameters"""
    original_text: str
    intent: Intent
    confidence: float
    entities: Dict[str, Any]
    parameters: Dict[str, Any]
    context: Dict[str, Any]
    reasoning: Optional[str] = None
    suggested_actions: List[str] = None
    
    def __post_init__(self):
        if self.suggested_actions is None:
            self.suggested_actions = []

@dataclass
class ConversationContext:
    """Conversation context for LLM"""
    blender_context: Dict[str, Any]
    conversation_history: List[Dict[str, str]]
    user_preferences: Dict[str, Any]
    current_task: Optional[str] = None
    
    def to_prompt_context(self) -> str:
        """Convert context to prompt format"""
        context_parts = []
        
        # Blender context
        if self.blender_context:
            context_parts.append("BLENDER CONTEXT:")
            context_parts.append(f"- Scene: {self.blender_context.get('scene_name', 'Unknown')}")
            context_parts.append(f"- Mode: {self.blender_context.get('mode', 'OBJECT')}")
            context_parts.append(f"- Selected Objects: {self.blender_context.get('selected_objects', [])}")
            context_parts.append(f"- Active Object: {self.blender_context.get('active_object', 'None')}")
            context_parts.append(f"- Total Objects: {self.blender_context.get('total_objects', 0)}")
        
        # Current task
        if self.current_task:
            context_parts.append(f"\nCURRENT TASK: {self.current_task}")
        
        # User preferences
        if self.user_preferences:
            context_parts.append("\nUSER PREFERENCES:")
            for key, value in self.user_preferences.items():
                context_parts.append(f"- {key}: {value}")
        
        return "\n".join(context_parts)

class LLMNLPProcessor:
    """LLM-based natural language processor for Turkish AI commands"""
    
    def __init__(self, llm_config: LLMConfig):
        self.llm_config = llm_config
        self.llm_provider: Optional[LLMProvider] = None
        self.conversation_context = ConversationContext(
            blender_context={},
            conversation_history=[],
            user_preferences={}
        )
        self.is_initialized = False
        
        # Response format schema
        self.response_schema = {
            "intent": "string (one of: create_object, modify_object, animate, material, lighting, render, scene_management, help, conversation, unknown)",
            "confidence": "float (0.0 to 1.0)",
            "entities": {
                "object_types": "list of strings",
                "materials": "list of strings", 
                "colors": "list of strings",
                "numbers": "list of numbers",
                "actions": "list of strings",
                "properties": "list of strings"
            },
            "parameters": {
                "action": "string",
                "target": "string",
                "values": "dict",
                "options": "dict"
            },
            "reasoning": "string explaining the analysis",
            "suggested_actions": "list of strings with specific Blender actions",
            "response_text": "string with Turkish response to user"
        }
    
    async def initialize(self) -> bool:
        """Initialize LLM NLP processor"""
        try:
            # Create LLM provider
            self.llm_provider = get_llm_provider(self.llm_config)
            if not self.llm_provider:
                logger.error("Failed to create LLM provider")
                return False
            
            # Initialize provider
            if not await self.llm_provider.initialize():
                logger.error("Failed to initialize LLM provider")
                return False
            
            self.is_initialized = True
            logger.info(f"LLM NLP processor initialized with {self.llm_config.provider_type.value}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize LLM NLP processor: {e}")
            return False
    
    def _create_system_prompt(self) -> str:
        """Create system prompt for LLM"""
        return """Sen Blender 3D yazılımı için geliştirilmiş Türkçe konuşan bir AI asistanısın. 
Kullanıcıların doğal dil komutlarını analiz edip Blender işlemlerine dönüştürüyorsun.

GÖREVLER:
1. Kullanıcının Türkçe mesajını analiz et
2. Intent (niyet) belirle
3. Gerekli parametreleri çıkar
4. Blender context'ini dikkate al
5. Yapılandırılmış JSON yanıt ver

DESTEKLENEN İNTENTLER:
- create_object: Obje oluşturma (küp, küre, karakter, bina vb.)
- modify_object: Obje değiştirme (boyut, konum, rotasyon vb.)
- animate: Animasyon oluşturma
- material: Materyal/tekstür işlemleri
- lighting: Aydınlatma ayarları
- render: Render işlemleri
- scene_management: Sahne yönetimi
- help: Yardım istekleri
- conversation: Genel sohbet
- unknown: Anlaşılmayan istekler

TÜRKÇE DİL DESTEĞİ:
- Türkçe dilbilgisi kurallarını uygula
- Türkçe obje isimlerini İngilizce Blender terimlerine çevir
- Kültürel bağlamı dikkate al

YANIT FORMATI:
Sadece geçerli JSON formatında yanıt ver. Ek açıklama yapma."""

    def _create_user_prompt(self, message: str, context: ConversationContext) -> str:
        """Create user prompt with context"""
        prompt_parts = []
        
        # Add context
        context_str = context.to_prompt_context()
        if context_str:
            prompt_parts.append(context_str)
            prompt_parts.append("")
        
        # Add conversation history (last 3 messages)
        if context.conversation_history:
            prompt_parts.append("SON KONUŞMA:")
            for msg in context.conversation_history[-3:]:
                role = "Kullanıcı" if msg["role"] == "user" else "Asistan"
                prompt_parts.append(f"{role}: {msg['content']}")
            prompt_parts.append("")
        
        # Add current message
        prompt_parts.append(f"KULLANICI MESAJI: {message}")
        prompt_parts.append("")
        
        # Add response format
        prompt_parts.append("YANIT FORMATI:")
        prompt_parts.append(json.dumps(self.response_schema, indent=2, ensure_ascii=False))
        
        return "\n".join(prompt_parts)
    
    async def parse_message(self, message: str, context: Optional[Dict] = None) -> ParsedMessage:
        """Parse user message using LLM"""
        try:
            if not self.is_initialized:
                logger.error("LLM NLP processor not initialized")
                return self._create_fallback_response(message, "Processor not initialized")
            
            # Update context
            if context:
                self.conversation_context.blender_context.update(context)
            
            # Create prompts
            system_prompt = self._create_system_prompt()
            user_prompt = self._create_user_prompt(message, self.conversation_context)
            
            # Prepare messages
            messages = [
                {"role": "user", "content": user_prompt}
            ]
            
            # Get LLM response
            start_time = time.time()
            llm_response = await self.llm_provider.generate_structured_response(
                messages=messages,
                system_prompt=system_prompt,
                response_format=self.response_schema
            )
            
            processing_time = time.time() - start_time
            logger.info(f"LLM processing took {processing_time:.2f} seconds")
            
            if not llm_response.success:
                logger.error(f"LLM response failed: {llm_response.error}")
                return self._create_fallback_response(message, llm_response.error)
            
            # Parse JSON response
            parsed_json = llm_response.parse_json()
            if not parsed_json:
                logger.error("Failed to parse LLM response as JSON")
                return self._create_fallback_response(message, "Invalid JSON response")
            
            # Create ParsedMessage from LLM response
            parsed_message = self._create_parsed_message_from_json(message, parsed_json)
            
            # Add to conversation history
            self._update_conversation_history(message, parsed_json.get("response_text", ""))
            
            return parsed_message
            
        except Exception as e:
            logger.error(f"Failed to parse message with LLM: {e}")
            return self._create_fallback_response(message, str(e))
    
    def _create_parsed_message_from_json(self, original_text: str, json_data: Dict) -> ParsedMessage:
        """Create ParsedMessage from LLM JSON response"""
        try:
            # Parse intent
            intent_str = json_data.get("intent", "unknown")
            try:
                intent = Intent(intent_str)
            except ValueError:
                intent = Intent.UNKNOWN
            
            # Extract data
            confidence = float(json_data.get("confidence", 0.0))
            entities = json_data.get("entities", {})
            parameters = json_data.get("parameters", {})
            reasoning = json_data.get("reasoning", "")
            suggested_actions = json_data.get("suggested_actions", [])
            
            return ParsedMessage(
                original_text=original_text,
                intent=intent,
                confidence=confidence,
                entities=entities,
                parameters=parameters,
                context=self.conversation_context.blender_context.copy(),
                reasoning=reasoning,
                suggested_actions=suggested_actions
            )
            
        except Exception as e:
            logger.error(f"Failed to create ParsedMessage from JSON: {e}")
            return self._create_fallback_response(original_text, str(e))
    
    def _create_fallback_response(self, message: str, error: str) -> ParsedMessage:
        """Create fallback response when LLM fails"""
        return ParsedMessage(
            original_text=message,
            intent=Intent.UNKNOWN,
            confidence=0.0,
            entities={},
            parameters={},
            context=self.conversation_context.blender_context.copy(),
            reasoning=f"Fallback due to error: {error}",
            suggested_actions=[]
        )
    
    def _update_conversation_history(self, user_message: str, assistant_response: str):
        """Update conversation history"""
        self.conversation_context.conversation_history.append({
            "role": "user",
            "content": user_message
        })
        
        if assistant_response:
            self.conversation_context.conversation_history.append({
                "role": "assistant", 
                "content": assistant_response
            })
        
        # Keep only last 10 messages
        if len(self.conversation_context.conversation_history) > 10:
            self.conversation_context.conversation_history = self.conversation_context.conversation_history[-10:]
    
    async def generate_response(self, parsed_message: ParsedMessage) -> str:
        """Generate response text from parsed message"""
        try:
            # If LLM already provided response text, use it
            if hasattr(parsed_message, 'response_text'):
                return getattr(parsed_message, 'response_text', "")
            
            # Otherwise generate response based on intent
            return self._generate_intent_based_response(parsed_message)
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return "Üzgünüm, yanıt oluştururken bir hata oluştu."
    
    def _generate_intent_based_response(self, parsed_message: ParsedMessage) -> str:
        """Generate response based on intent"""
        intent_responses = {
            Intent.CREATE_OBJECT: "Anladım! Obje oluşturma işlemini başlatıyorum.",
            Intent.MODIFY_OBJECT: "Tabii! Seçili objeyi değiştiriyorum.",
            Intent.ANIMATE: "Harika! Animasyon oluşturuyorum.",
            Intent.MATERIAL: "Materyal oluşturma işlemini başlatıyorum.",
            Intent.LIGHTING: "Aydınlatma ayarlarını yapıyorum.",
            Intent.RENDER: "Render ayarlarını yapılandırıyorum.",
            Intent.SCENE_MANAGEMENT: "Sahne yönetimi işlemini gerçekleştiriyorum.",
            Intent.HELP: "Size yardım etmekten memnuniyet duyarım!",
            Intent.CONVERSATION: "Elbette, sohbet edebiliriz!",
            Intent.UNKNOWN: "Üzgünüm, tam olarak ne istediğinizi anlayamadım. Biraz daha detay verebilir misiniz?"
        }
        
        return intent_responses.get(parsed_message.intent, intent_responses[Intent.UNKNOWN])
    
    def update_context(self, key: str, value: Any):
        """Update conversation context"""
        if key == "blender_context":
            self.conversation_context.blender_context.update(value)
        elif key == "user_preferences":
            self.conversation_context.user_preferences.update(value)
        elif key == "current_task":
            self.conversation_context.current_task = value
    
    def get_context(self, key: str) -> Any:
        """Get value from conversation context"""
        if key == "blender_context":
            return self.conversation_context.blender_context
        elif key == "user_preferences":
            return self.conversation_context.user_preferences
        elif key == "current_task":
            return self.conversation_context.current_task
        return None
    
    def clear_context(self):
        """Clear conversation context"""
        self.conversation_context = ConversationContext(
            blender_context={},
            conversation_history=[],
            user_preferences={}
        )
    
    async def cleanup(self):
        """Cleanup LLM NLP processor"""
        if self.llm_provider:
            self.llm_provider.cleanup()
            self.llm_provider = None
        
        self.clear_context()
        self.is_initialized = False
        logger.info("LLM NLP processor cleaned up")
