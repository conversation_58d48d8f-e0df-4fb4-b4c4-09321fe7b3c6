"""
Agent Panel - UI for agent creation, configuration, and management
"""

import bpy
from bpy.types import Panel, PropertyGroup
from bpy.props import StringProperty, EnumProperty, BoolProperty, IntProperty

from ..crew_system.agent_factory import get_agent_factory
from ..crew_system.crew_manager import get_crew_manager
from ..crew_system.task_executor import get_task_executor

class AI_AGENT_PT_agent_panel(Panel):
    """Agent management panel"""
    bl_label = "Agent Management"
    bl_idname = "AI_AGENT_PT_agent_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_main_panel"
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        
        # Agent Templates section
        box = layout.box()
        box.label(text="Agent Templates", icon='ARMATURE_DATA')
        
        agent_factory = get_agent_factory()
        templates = agent_factory.list_templates()
        
        # Default templates
        col = box.column(align=True)
        col.label(text="Default Templates:")
        for template in templates['default']:
            row = col.row()
            row.label(text=f"• {template}")
            row.operator("ai_agent.create_from_template", text="Create", icon='ADD').template_name = template
        
        # Custom templates
        if templates['custom']:
            col.separator()
            col.label(text="Custom Templates:")
            for template in templates['custom']:
                row = col.row()
                row.label(text=f"• {template}")
                row.operator("ai_agent.create_from_template", text="Create", icon='ADD').template_name = template
        
        # Quick Crew Creation section
        box = layout.box()
        box.label(text="Quick Crew Creation", icon='OUTLINER_COLLECTION')
        
        col = box.column(align=True)
        
        # Modeling crew
        row = col.row()
        row.operator("ai_agent.create_modeling_crew", text="Modeling Crew", icon='OUTLINER_OB_MESH')

        # Complete asset crew
        row = col.row()
        row.operator("ai_agent.create_complete_crew", text="Complete Asset Crew", icon='SCENE_DATA')

        # Animation crew
        row = col.row()
        row.operator("ai_agent.create_animation_crew", text="Animation Crew", icon='ARMATURE_DATA')

        # Custom crew
        row = col.row()
        row.operator("ai_agent.create_custom_crew", text="Custom Crew", icon='PREFERENCES')

class AI_AGENT_PT_active_crews_panel(Panel):
    """Active crews monitoring panel"""
    bl_label = "Active Crews"
    bl_idname = "AI_AGENT_PT_active_crews_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_agent_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        
        crew_manager = get_crew_manager()
        active_crews = crew_manager.list_active_crews()
        
        if not active_crews:
            layout.label(text="No active crews", icon='INFO')
            return
        
        for crew_id in active_crews:
            box = layout.box()
            
            # Crew header
            row = box.row()
            row.label(text=crew_id, icon='OUTLINER_COLLECTION')
            
            # Crew status
            status = crew_manager.get_crew_status(crew_id)
            col = box.column(align=True)
            col.label(text=f"Agents: {status.get('agents_count', 0)}")
            col.label(text=f"Tasks: {status.get('tasks_count', 0)}")
            
            # Crew actions
            row = box.row()
            execute_op = row.operator("ai_agent.execute_crew", text="Execute", icon='PLAY')
            execute_op.crew_id = crew_id
            
            remove_op = row.operator("ai_agent.remove_crew", text="Remove", icon='REMOVE')
            remove_op.crew_id = crew_id

class AI_AGENT_PT_task_monitor_panel(Panel):
    """Task monitoring panel"""
    bl_label = "Task Monitor"
    bl_idname = "AI_AGENT_PT_task_monitor_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_agent_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        
        task_executor = get_task_executor()
        active_tasks = task_executor.get_active_tasks()
        
        if not active_tasks:
            layout.label(text="No active tasks", icon='INFO')
            return
        
        for task_id, task_result in active_tasks.items():
            box = layout.box()
            
            # Task header
            row = box.row()
            row.label(text=task_id, icon='SEQUENCE')
            
            # Task status
            status_icon = {
                'pending': 'TIME',
                'running': 'PLAY',
                'completed': 'CHECKMARK',
                'failed': 'ERROR',
                'cancelled': 'PANEL_CLOSE'
            }.get(task_result.status.value, 'QUESTION')
            
            row = box.row()
            row.label(text=f"Status: {task_result.status.value.title()}", icon=status_icon)
            
            # Task details
            if task_result.duration:
                row = box.row()
                row.label(text=f"Duration: {task_result.duration:.2f}s")
            
            if task_result.error:
                row = box.row()
                row.label(text=f"Error: {task_result.error}", icon='ERROR')
            
            # Task actions
            if task_result.status.value in ['pending', 'running']:
                row = box.row()
                cancel_op = row.operator("ai_agent.cancel_task", text="Cancel", icon='PANEL_CLOSE')
                cancel_op.task_id = task_id

class AI_AGENT_PT_agent_config_panel(Panel):
    """Agent configuration panel"""
    bl_label = "Agent Configuration"
    bl_idname = "AI_AGENT_PT_agent_config_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "AI Agents"
    bl_parent_id = "AI_AGENT_PT_agent_panel"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw(self, context):
        layout = self.layout
        scene = context.scene
        
        # Agent configuration settings
        if hasattr(scene, 'ai_agent_settings'):
            settings = scene.ai_agent_settings
            
            box = layout.box()
            box.label(text="Agent Settings", icon='SETTINGS')
            
            col = box.column(align=True)
            
            # API Key (if needed)
            if hasattr(settings, 'api_key'):
                col.prop(settings, "api_key", text="API Key")
            
            # Max agents
            if hasattr(settings, 'max_agents'):
                col.prop(settings, "max_agents", text="Max Concurrent Agents")
            
            # Timeout
            if hasattr(settings, 'timeout_seconds'):
                col.prop(settings, "timeout_seconds", text="Timeout (seconds)")
            
            # Log level
            if hasattr(settings, 'log_level'):
                col.prop(settings, "log_level", text="Log Level")
        
        # Performance monitoring
        box = layout.box()
        box.label(text="Performance", icon='GRAPH')
        
        col = box.column(align=True)
        col.operator("ai_agent.show_performance", text="Show Performance Stats", icon='GRAPH')
        col.operator("ai_agent.cleanup_tasks", text="Cleanup Old Tasks", icon='TRASH')

# Property groups for agent configuration
class AgentCreationProperties(PropertyGroup):
    """Properties for agent creation"""
    
    agent_role: StringProperty(
        name="Role",
        description="Role of the agent",
        default="Assistant"
    )
    
    agent_goal: StringProperty(
        name="Goal",
        description="Goal of the agent",
        default="Help with tasks"
    )
    
    agent_backstory: StringProperty(
        name="Backstory",
        description="Backstory of the agent",
        default="An AI assistant"
    )
    
    verbose: BoolProperty(
        name="Verbose",
        description="Enable verbose output",
        default=True
    )
    
    allow_delegation: BoolProperty(
        name="Allow Delegation",
        description="Allow agent to delegate tasks",
        default=False
    )

class CrewCreationProperties(PropertyGroup):
    """Properties for crew creation"""
    
    crew_name: StringProperty(
        name="Crew Name",
        description="Name of the crew",
        default="My Crew"
    )
    
    crew_type: EnumProperty(
        name="Crew Type",
        description="Type of crew to create",
        items=[
            ('modeling', 'Modeling', 'Crew for 3D modeling tasks'),
            ('texturing', 'Texturing', 'Crew for material and texture work'),
            ('animation', 'Animation', 'Crew for animation tasks'),
            ('complete_asset', 'Complete Asset', 'Full pipeline crew'),
            ('custom', 'Custom', 'Custom crew configuration')
        ],
        default='modeling'
    )
    
    process_type: EnumProperty(
        name="Process",
        description="Execution process for the crew",
        items=[
            ('sequential', 'Sequential', 'Execute tasks one after another'),
            ('hierarchical', 'Hierarchical', 'Execute tasks in hierarchy')
        ],
        default='sequential'
    )

# Classes to register
classes = [
    AgentCreationProperties,
    CrewCreationProperties,
    AI_AGENT_PT_agent_panel,
    AI_AGENT_PT_active_crews_panel,
    AI_AGENT_PT_task_monitor_panel,
    AI_AGENT_PT_agent_config_panel,
]

def register():
    """Register agent panel classes"""
    for cls in classes:
        bpy.utils.register_class(cls)
    
    # Add properties to scene
    bpy.types.Scene.agent_creation_props = bpy.props.PointerProperty(type=AgentCreationProperties)
    bpy.types.Scene.crew_creation_props = bpy.props.PointerProperty(type=CrewCreationProperties)

def unregister():
    """Unregister agent panel classes"""
    # Remove properties
    if hasattr(bpy.types.Scene, 'agent_creation_props'):
        del bpy.types.Scene.agent_creation_props
    if hasattr(bpy.types.Scene, 'crew_creation_props'):
        del bpy.types.Scene.crew_creation_props
    
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
