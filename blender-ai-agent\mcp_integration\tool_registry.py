"""
Tool Registry - Registry for MCP tools and integration with Blender AI agents
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass
from enum import Enum

# MCP types (fallback if not available)
try:
    from mcp.types import Tool, CallToolResult, TextContent, ImageContent
    MCP_AVAILABLE = True
except ImportError:
    class Tool:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class CallToolResult:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class TextContent:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    class ImageContent:
        def __init__(self, **kwargs):
            self.__dict__.update(kwargs)
    
    MCP_AVAILABLE = False

from .mcp_client import get_mcp_client
from .server_manager import get_server_manager
from ..crew_system.blender_tools import get_blender_tools
from ..utils.logging_config import get_logger

logger = get_logger("tool_registry")

class ToolCategory(Enum):
    """Categories for organizing tools"""
    BLENDER_MESH = "blender_mesh"
    BLENDER_MATERIAL = "blender_material"
    BLENDER_LIGHTING = "blender_lighting"
    BLENDER_ANIMATION = "blender_animation"
    BLENDER_RENDER = "blender_render"
    BLENDER_SCENE = "blender_scene"
    FILESYSTEM = "filesystem"
    WEB = "web"
    DATABASE = "database"
    DEVELOPMENT = "development"
    UTILITY = "utility"
    CUSTOM = "custom"

@dataclass
class RegisteredTool:
    """Information about a registered tool"""
    name: str
    description: str
    server_name: str
    category: ToolCategory
    mcp_tool: Tool
    parameters_schema: Dict[str, Any]
    is_available: bool = True
    last_used: Optional[float] = None
    usage_count: int = 0
    average_execution_time: float = 0.0

@dataclass
class ToolExecutionResult:
    """Result of tool execution"""
    success: bool
    result: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    server_name: Optional[str] = None
    tool_name: Optional[str] = None

class ToolRegistry:
    """Registry for managing MCP tools and their integration with AI agents"""
    
    def __init__(self):
        self.mcp_client = get_mcp_client()
        self.server_manager = get_server_manager()
        self.blender_tools = get_blender_tools()
        
        # Tool storage
        self.registered_tools: Dict[str, RegisteredTool] = {}
        self.tools_by_category: Dict[ToolCategory, List[str]] = {}
        self.tools_by_server: Dict[str, List[str]] = {}
        
        # Callbacks
        self.on_tool_registered: Optional[Callable[[str, RegisteredTool], None]] = None
        self.on_tool_unregistered: Optional[Callable[[str], None]] = None
        self.on_tool_executed: Optional[Callable[[str, ToolExecutionResult], None]] = None
        
        # Setup callbacks for server events
        self.mcp_client.on_tool_discovered = self._on_tool_discovered
        self.server_manager.on_server_removed = self._on_server_removed
        
        # Register built-in Blender tools
        self._register_blender_tools()
    
    def _register_blender_tools(self):
        """Register built-in Blender tools as MCP-compatible tools"""
        try:
            # Mesh tools
            self._register_blender_tool(
                "create_primitive_mesh",
                "Create primitive mesh objects (cube, sphere, cylinder, etc.)",
                ToolCategory.BLENDER_MESH,
                {
                    "type": "object",
                    "properties": {
                        "mesh_type": {"type": "string", "enum": ["cube", "sphere", "cylinder", "plane", "monkey"]},
                        "location": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
                        "scale": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3}
                    },
                    "required": ["mesh_type"]
                }
            )
            
            self._register_blender_tool(
                "apply_modifier",
                "Apply modifiers to mesh objects",
                ToolCategory.BLENDER_MESH,
                {
                    "type": "object",
                    "properties": {
                        "object_name": {"type": "string"},
                        "modifier_type": {"type": "string", "enum": ["subdivision", "mirror", "bevel", "array"]},
                        "levels": {"type": "integer", "minimum": 1, "maximum": 6},
                        "width": {"type": "number", "minimum": 0.001},
                        "segments": {"type": "integer", "minimum": 1}
                    },
                    "required": ["object_name", "modifier_type"]
                }
            )
            
            # Material tools
            self._register_blender_tool(
                "create_material",
                "Create new materials with PBR properties",
                ToolCategory.BLENDER_MATERIAL,
                {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "base_color": {"type": "array", "items": {"type": "number"}, "minItems": 4, "maxItems": 4},
                        "metallic": {"type": "number", "minimum": 0, "maximum": 1},
                        "roughness": {"type": "number", "minimum": 0, "maximum": 1}
                    },
                    "required": ["name"]
                }
            )
            
            self._register_blender_tool(
                "assign_material",
                "Assign materials to objects",
                ToolCategory.BLENDER_MATERIAL,
                {
                    "type": "object",
                    "properties": {
                        "object_name": {"type": "string"},
                        "material_name": {"type": "string"}
                    },
                    "required": ["object_name", "material_name"]
                }
            )
            
            # Lighting tools
            self._register_blender_tool(
                "add_light",
                "Add lights to the scene",
                ToolCategory.BLENDER_LIGHTING,
                {
                    "type": "object",
                    "properties": {
                        "light_type": {"type": "string", "enum": ["SUN", "POINT", "SPOT", "AREA"]},
                        "location": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3},
                        "energy": {"type": "number", "minimum": 0},
                        "color": {"type": "array", "items": {"type": "number"}, "minItems": 3, "maxItems": 3}
                    },
                    "required": ["light_type"]
                }
            )
            
            # Animation tools
            self._register_blender_tool(
                "add_keyframe",
                "Add keyframes for animation",
                ToolCategory.BLENDER_ANIMATION,
                {
                    "type": "object",
                    "properties": {
                        "object_name": {"type": "string"},
                        "property_path": {"type": "string"},
                        "frame": {"type": "integer", "minimum": 1},
                        "value": {"type": "number"}
                    },
                    "required": ["object_name", "property_path", "frame", "value"]
                }
            )
            
            # Scene tools
            self._register_blender_tool(
                "organize_objects_in_collection",
                "Organize objects into collections",
                ToolCategory.BLENDER_SCENE,
                {
                    "type": "object",
                    "properties": {
                        "object_names": {"type": "array", "items": {"type": "string"}},
                        "collection_name": {"type": "string"}
                    },
                    "required": ["object_names", "collection_name"]
                }
            )
            
            # Render tools
            self._register_blender_tool(
                "setup_render_settings",
                "Configure render settings",
                ToolCategory.BLENDER_RENDER,
                {
                    "type": "object",
                    "properties": {
                        "engine": {"type": "string", "enum": ["CYCLES", "EEVEE", "WORKBENCH"]},
                        "samples": {"type": "integer", "minimum": 1, "maximum": 4096},
                        "resolution": {"type": "array", "items": {"type": "integer"}, "minItems": 2, "maxItems": 2}
                    }
                }
            )
            
            logger.info(f"Registered {len(self.registered_tools)} built-in Blender tools")
            
        except Exception as e:
            logger.error(f"Failed to register Blender tools: {e}")
    
    def _register_blender_tool(self, name: str, description: str, category: ToolCategory, schema: Dict[str, Any]):
        """Register a Blender tool as an MCP-compatible tool"""
        try:
            # Create mock MCP tool object
            mcp_tool = Tool(
                name=name,
                description=description,
                inputSchema=schema
            )
            
            registered_tool = RegisteredTool(
                name=name,
                description=description,
                server_name="blender_builtin",
                category=category,
                mcp_tool=mcp_tool,
                parameters_schema=schema
            )
            
            self._add_tool_to_registry(registered_tool)
            
        except Exception as e:
            logger.error(f"Failed to register Blender tool {name}: {e}")
    
    def _on_tool_discovered(self, server_name: str, tool: Tool):
        """Callback when a tool is discovered on an MCP server"""
        try:
            # Determine category based on tool name/description
            category = self._categorize_tool(tool)
            
            registered_tool = RegisteredTool(
                name=tool.name,
                description=tool.description,
                server_name=server_name,
                category=category,
                mcp_tool=tool,
                parameters_schema=getattr(tool, 'inputSchema', {})
            )
            
            self._add_tool_to_registry(registered_tool)
            
            logger.info(f"Discovered and registered tool: {tool.name} from server {server_name}")
            
        except Exception as e:
            logger.error(f"Failed to register discovered tool {tool.name}: {e}")
    
    def _on_server_removed(self, server_name: str):
        """Callback when a server is removed"""
        try:
            # Remove all tools from this server
            tools_to_remove = [
                tool_name for tool_name, tool in self.registered_tools.items()
                if tool.server_name == server_name
            ]
            
            for tool_name in tools_to_remove:
                self.unregister_tool(tool_name)
            
            logger.info(f"Removed {len(tools_to_remove)} tools from server {server_name}")
            
        except Exception as e:
            logger.error(f"Error removing tools from server {server_name}: {e}")
    
    def _categorize_tool(self, tool: Tool) -> ToolCategory:
        """Categorize a tool based on its name and description"""
        name_lower = tool.name.lower()
        desc_lower = tool.description.lower()
        
        # Blender-specific categorization
        if any(keyword in name_lower for keyword in ['mesh', 'vertex', 'face', 'edge', 'model']):
            return ToolCategory.BLENDER_MESH
        elif any(keyword in name_lower for keyword in ['material', 'texture', 'shader', 'pbr']):
            return ToolCategory.BLENDER_MATERIAL
        elif any(keyword in name_lower for keyword in ['light', 'lamp', 'hdri', 'shadow']):
            return ToolCategory.BLENDER_LIGHTING
        elif any(keyword in name_lower for keyword in ['keyframe', 'animation', 'rig', 'bone']):
            return ToolCategory.BLENDER_ANIMATION
        elif any(keyword in name_lower for keyword in ['render', 'cycles', 'eevee']):
            return ToolCategory.BLENDER_RENDER
        elif any(keyword in name_lower for keyword in ['scene', 'collection', 'object']):
            return ToolCategory.BLENDER_SCENE
        
        # General categorization
        elif any(keyword in name_lower for keyword in ['file', 'directory', 'path', 'read', 'write']):
            return ToolCategory.FILESYSTEM
        elif any(keyword in name_lower for keyword in ['web', 'http', 'url', 'search', 'api']):
            return ToolCategory.WEB
        elif any(keyword in name_lower for keyword in ['database', 'sql', 'query', 'table']):
            return ToolCategory.DATABASE
        elif any(keyword in name_lower for keyword in ['git', 'commit', 'branch', 'code']):
            return ToolCategory.DEVELOPMENT
        else:
            return ToolCategory.UTILITY
    
    def _add_tool_to_registry(self, tool: RegisteredTool):
        """Add a tool to the registry"""
        tool_name = tool.name
        
        # Add to main registry
        self.registered_tools[tool_name] = tool
        
        # Add to category index
        if tool.category not in self.tools_by_category:
            self.tools_by_category[tool.category] = []
        self.tools_by_category[tool.category].append(tool_name)
        
        # Add to server index
        if tool.server_name not in self.tools_by_server:
            self.tools_by_server[tool.server_name] = []
        self.tools_by_server[tool.server_name].append(tool_name)
        
        # Notify callback
        if self.on_tool_registered:
            self.on_tool_registered(tool_name, tool)
    
    def unregister_tool(self, tool_name: str) -> bool:
        """Unregister a tool"""
        try:
            if tool_name not in self.registered_tools:
                return False
            
            tool = self.registered_tools[tool_name]
            
            # Remove from indices
            if tool.category in self.tools_by_category:
                if tool_name in self.tools_by_category[tool.category]:
                    self.tools_by_category[tool.category].remove(tool_name)
            
            if tool.server_name in self.tools_by_server:
                if tool_name in self.tools_by_server[tool.server_name]:
                    self.tools_by_server[tool.server_name].remove(tool_name)
            
            # Remove from main registry
            del self.registered_tools[tool_name]
            
            # Notify callback
            if self.on_tool_unregistered:
                self.on_tool_unregistered(tool_name)
            
            logger.debug(f"Unregistered tool: {tool_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to unregister tool {tool_name}: {e}")
            return False
    
    async def execute_tool(self, tool_name: str, arguments: Dict[str, Any]) -> ToolExecutionResult:
        """Execute a tool with given arguments"""
        start_time = time.time()
        
        try:
            if tool_name not in self.registered_tools:
                return ToolExecutionResult(
                    success=False,
                    error=f"Tool not found: {tool_name}",
                    tool_name=tool_name
                )
            
            tool = self.registered_tools[tool_name]
            
            # Execute based on server type
            if tool.server_name == "blender_builtin":
                result = await self._execute_blender_tool(tool_name, arguments)
            else:
                result = await self._execute_mcp_tool(tool, arguments)
            
            # Update tool statistics
            execution_time = time.time() - start_time
            tool.usage_count += 1
            tool.last_used = time.time()
            tool.average_execution_time = (
                (tool.average_execution_time * (tool.usage_count - 1) + execution_time) / tool.usage_count
            )
            
            result.execution_time = execution_time
            result.server_name = tool.server_name
            result.tool_name = tool_name
            
            # Notify callback
            if self.on_tool_executed:
                self.on_tool_executed(tool_name, result)
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_result = ToolExecutionResult(
                success=False,
                error=str(e),
                execution_time=execution_time,
                tool_name=tool_name
            )
            
            logger.error(f"Tool execution failed for {tool_name}: {e}")
            return error_result
    
    async def _execute_blender_tool(self, tool_name: str, arguments: Dict[str, Any]) -> ToolExecutionResult:
        """Execute a built-in Blender tool"""
        try:
            # Get the method from blender_tools
            if hasattr(self.blender_tools, tool_name):
                method = getattr(self.blender_tools, tool_name)
                result = method(**arguments)
                
                return ToolExecutionResult(
                    success=result.success,
                    result=result.data,
                    error=result.error if not result.success else None
                )
            else:
                return ToolExecutionResult(
                    success=False,
                    error=f"Blender tool method not found: {tool_name}"
                )
                
        except Exception as e:
            return ToolExecutionResult(
                success=False,
                error=f"Blender tool execution error: {e}"
            )
    
    async def _execute_mcp_tool(self, tool: RegisteredTool, arguments: Dict[str, Any]) -> ToolExecutionResult:
        """Execute an MCP tool"""
        try:
            result = await self.mcp_client.call_tool(tool.server_name, tool.name, arguments)
            
            if result:
                return ToolExecutionResult(
                    success=True,
                    result=result
                )
            else:
                return ToolExecutionResult(
                    success=False,
                    error="MCP tool call returned no result"
                )
                
        except Exception as e:
            return ToolExecutionResult(
                success=False,
                error=f"MCP tool execution error: {e}"
            )
    
    def list_tools(self, category: Optional[ToolCategory] = None, server_name: Optional[str] = None) -> List[str]:
        """List available tools, optionally filtered by category or server"""
        if category:
            return self.tools_by_category.get(category, [])
        elif server_name:
            return self.tools_by_server.get(server_name, [])
        else:
            return list(self.registered_tools.keys())
    
    def get_tool_info(self, tool_name: str) -> Optional[RegisteredTool]:
        """Get information about a tool"""
        return self.registered_tools.get(tool_name)
    
    def search_tools(self, query: str) -> List[str]:
        """Search tools by name or description"""
        query_lower = query.lower()
        results = []
        
        for tool_name, tool in self.registered_tools.items():
            if (query_lower in tool_name.lower() or
                query_lower in tool.description.lower()):
                results.append(tool_name)
        
        return results
    
    def get_tools_by_category(self) -> Dict[str, List[str]]:
        """Get tools organized by category"""
        return {category.value: tools for category, tools in self.tools_by_category.items()}
    
    def get_tool_statistics(self) -> Dict[str, Any]:
        """Get tool usage statistics"""
        total_tools = len(self.registered_tools)
        used_tools = sum(1 for tool in self.registered_tools.values() if tool.usage_count > 0)
        total_executions = sum(tool.usage_count for tool in self.registered_tools.values())
        
        return {
            'total_tools': total_tools,
            'used_tools': used_tools,
            'total_executions': total_executions,
            'tools_by_category': {cat.value: len(tools) for cat, tools in self.tools_by_category.items()},
            'tools_by_server': {server: len(tools) for server, tools in self.tools_by_server.items()}
        }

# Global tool registry instance
_tool_registry = ToolRegistry()

def get_tool_registry():
    """Get the tool registry instance"""
    return _tool_registry

def register():
    """Register tool registry"""
    pass

def unregister():
    """Unregister tool registry"""
    pass
