"""
MCP Integration Module - Model Context Protocol integration for Blender AI Agent System
"""

from . import mcp_client
from . import server_manager
from . import tool_registry
from . import context_provider

# List of modules to register
modules = [
    mcp_client,
    server_manager,
    tool_registry,
    context_provider,
]

def register():
    """Register all MCP integration components"""
    for module in modules:
        if hasattr(module, 'register'):
            module.register()

def unregister():
    """Unregister all MCP integration components"""
    for module in reversed(modules):
        if hasattr(module, 'unregister'):
            module.unregister()
