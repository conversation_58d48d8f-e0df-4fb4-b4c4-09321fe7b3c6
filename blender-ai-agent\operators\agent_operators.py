"""
Agent Operators - Blender operators for agent and crew management
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty, EnumProperty

from ..crew_system.agent_factory import get_agent_factory
from ..crew_system.crew_manager import get_crew_manager
from ..crew_system.task_executor import get_task_executor
from ..utils.logging_config import get_logger

logger = get_logger("agent_operators")

class AI_AGENT_OT_create_from_template(Operator):
    """Create agent from template"""
    bl_idname = "ai_agent.create_from_template"
    bl_label = "Create Agent from Template"
    bl_description = "Create a new agent from a predefined template"
    bl_options = {'REGISTER'}
    
    template_name: StringProperty(
        name="Template Name",
        description="Name of the template to use"
    )
    
    def execute(self, context):
        try:
            agent_factory = get_agent_factory()
            
            # Get template
            template = agent_factory.get_template(self.template_name)
            if not template:
                self.report({'ERROR'}, f"Template not found: {self.template_name}")
                return {'CANCELLED'}
            
            # Create agent config
            agent_config = agent_factory.create_agent_config(self.template_name)
            if not agent_config:
                self.report({'ERROR'}, "Failed to create agent configuration")
                return {'CANCELLED'}
            
            # For now, just report success
            # In a full implementation, this would create and store the agent
            self.report({'INFO'}, f"Created agent: {template.role}")
            logger.info(f"Created agent from template: {self.template_name}")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to create agent: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_create_modeling_crew(Operator):
    """Create modeling crew"""
    bl_idname = "ai_agent.create_modeling_crew"
    bl_label = "Create Modeling Crew"
    bl_description = "Create a crew specialized in 3D modeling"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            agent_factory = get_agent_factory()
            crew_manager = get_crew_manager()
            
            # Create crew configuration
            crew_config = agent_factory.create_crew_config('modeling', ['modeler'])
            
            # Create crew
            crew_id = crew_manager.create_crew(crew_config)
            if crew_id:
                self.report({'INFO'}, f"Created modeling crew: {crew_id}")
                logger.info(f"Created modeling crew: {crew_id}")
            else:
                self.report({'ERROR'}, "Failed to create modeling crew")
                return {'CANCELLED'}
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to create modeling crew: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_create_complete_crew(Operator):
    """Create complete asset crew"""
    bl_idname = "ai_agent.create_complete_crew"
    bl_label = "Create Complete Asset Crew"
    bl_description = "Create a crew for complete asset pipeline"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            agent_factory = get_agent_factory()
            crew_manager = get_crew_manager()
            
            # Create crew configuration
            agents = ['modeler', 'material_artist', 'lighting_artist', 'render_specialist']
            crew_config = agent_factory.create_crew_config('complete_asset', agents)
            
            # Create crew
            crew_id = crew_manager.create_crew(crew_config)
            if crew_id:
                self.report({'INFO'}, f"Created complete asset crew: {crew_id}")
                logger.info(f"Created complete asset crew: {crew_id}")
            else:
                self.report({'ERROR'}, "Failed to create complete asset crew")
                return {'CANCELLED'}
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to create complete asset crew: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_create_animation_crew(Operator):
    """Create animation crew"""
    bl_idname = "ai_agent.create_animation_crew"
    bl_label = "Create Animation Crew"
    bl_description = "Create a crew specialized in animation"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            agent_factory = get_agent_factory()
            crew_manager = get_crew_manager()
            
            # Create crew configuration
            crew_config = agent_factory.create_crew_config('animation', ['animator'])
            
            # Create crew
            crew_id = crew_manager.create_crew(crew_config)
            if crew_id:
                self.report({'INFO'}, f"Created animation crew: {crew_id}")
                logger.info(f"Created animation crew: {crew_id}")
            else:
                self.report({'ERROR'}, "Failed to create animation crew")
                return {'CANCELLED'}
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to create animation crew: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_create_custom_crew(Operator):
    """Create custom crew"""
    bl_idname = "ai_agent.create_custom_crew"
    bl_label = "Create Custom Crew"
    bl_description = "Create a custom crew with selected agents"
    bl_options = {'REGISTER', 'UNDO'}
    
    def execute(self, context):
        # For now, just show a message
        # In a full implementation, this would open a dialog for custom crew creation
        self.report({'INFO'}, "Custom crew creation dialog would open here")
        return {'FINISHED'}

class AI_AGENT_OT_execute_crew(Operator):
    """Execute crew"""
    bl_idname = "ai_agent.execute_crew"
    bl_label = "Execute Crew"
    bl_description = "Execute a crew to perform its tasks"
    bl_options = {'REGISTER'}
    
    crew_id: StringProperty(
        name="Crew ID",
        description="ID of the crew to execute"
    )
    
    def execute(self, context):
        try:
            crew_manager = get_crew_manager()
            task_executor = get_task_executor()
            
            # Create task configuration for crew execution
            task_config = {
                'type': 'crew_execution',
                'crew_config': {'id': self.crew_id}
            }
            
            # Submit task
            def progress_callback(task_id, message, progress):
                print(f"Task {task_id}: {message} ({progress}%)")
            
            task_id = task_executor.submit_task(task_config, progress_callback)
            
            self.report({'INFO'}, f"Started execution of crew {self.crew_id} (Task: {task_id})")
            logger.info(f"Started execution of crew {self.crew_id}")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to execute crew: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_remove_crew(Operator):
    """Remove crew"""
    bl_idname = "ai_agent.remove_crew"
    bl_label = "Remove Crew"
    bl_description = "Remove a crew from active crews"
    bl_options = {'REGISTER'}
    
    crew_id: StringProperty(
        name="Crew ID",
        description="ID of the crew to remove"
    )
    
    def execute(self, context):
        try:
            crew_manager = get_crew_manager()
            
            success = crew_manager.remove_crew(self.crew_id)
            if success:
                self.report({'INFO'}, f"Removed crew: {self.crew_id}")
                logger.info(f"Removed crew: {self.crew_id}")
            else:
                self.report({'WARNING'}, f"Crew not found: {self.crew_id}")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to remove crew: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_cancel_task(Operator):
    """Cancel task"""
    bl_idname = "ai_agent.cancel_task"
    bl_label = "Cancel Task"
    bl_description = "Cancel a running or pending task"
    bl_options = {'REGISTER'}
    
    task_id: StringProperty(
        name="Task ID",
        description="ID of the task to cancel"
    )
    
    def execute(self, context):
        try:
            task_executor = get_task_executor()
            
            success = task_executor.cancel_task(self.task_id)
            if success:
                self.report({'INFO'}, f"Cancelled task: {self.task_id}")
                logger.info(f"Cancelled task: {self.task_id}")
            else:
                self.report({'WARNING'}, f"Could not cancel task: {self.task_id}")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to cancel task: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_show_performance(Operator):
    """Show performance statistics"""
    bl_idname = "ai_agent.show_performance"
    bl_label = "Show Performance"
    bl_description = "Show performance statistics for agents and tasks"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            task_executor = get_task_executor()
            crew_manager = get_crew_manager()
            
            # Get statistics
            active_tasks = task_executor.get_active_tasks()
            active_crews = crew_manager.list_active_crews()
            
            # Calculate stats
            total_tasks = len(active_tasks)
            completed_tasks = sum(1 for task in active_tasks.values() if task.status.value == 'completed')
            failed_tasks = sum(1 for task in active_tasks.values() if task.status.value == 'failed')
            
            # Show stats
            stats_message = f"Tasks: {total_tasks} total, {completed_tasks} completed, {failed_tasks} failed. Crews: {len(active_crews)} active"
            self.report({'INFO'}, stats_message)
            logger.info(f"Performance stats: {stats_message}")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to show performance: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

class AI_AGENT_OT_cleanup_tasks(Operator):
    """Cleanup old tasks"""
    bl_idname = "ai_agent.cleanup_tasks"
    bl_label = "Cleanup Tasks"
    bl_description = "Remove old completed and failed tasks"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        try:
            task_executor = get_task_executor()
            
            # Cleanup tasks older than 1 hour
            task_executor.cleanup_completed_tasks(max_age_hours=1)
            
            self.report({'INFO'}, "Cleaned up old tasks")
            logger.info("Cleaned up old tasks")
            
            return {'FINISHED'}
            
        except Exception as e:
            error_msg = f"Failed to cleanup tasks: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)
            return {'CANCELLED'}

# Classes to register
classes = [
    AI_AGENT_OT_create_from_template,
    AI_AGENT_OT_create_modeling_crew,
    AI_AGENT_OT_create_complete_crew,
    AI_AGENT_OT_create_animation_crew,
    AI_AGENT_OT_create_custom_crew,
    AI_AGENT_OT_execute_crew,
    AI_AGENT_OT_remove_crew,
    AI_AGENT_OT_cancel_task,
    AI_AGENT_OT_show_performance,
    AI_AGENT_OT_cleanup_tasks,
]

def register():
    """Register agent operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister agent operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
