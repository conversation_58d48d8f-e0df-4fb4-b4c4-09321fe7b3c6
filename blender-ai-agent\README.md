# AI Agent System for Blender

A comprehensive AI agent system for Blender 4.4+ that integrates Crew AI framework with Model Context Protocol (MCP) servers, providing a user-friendly interface for automated 3D workflows.

## Features

- **Multi-Agent Workflows**: Create teams of specialized AI agents
- **Crew AI Integration**: Leverage the power of collaborative AI agents
- **MCP Server Support**: Connect to external tools and services via Model Context Protocol
- **Blender Native Tools**: Comprehensive toolkit for 3D operations
- **User-Friendly Interface**: Intuitive panels and controls
- **Extensible Architecture**: Plugin system for custom tools

## Requirements

- Blender 4.4.0 or higher
- Python 3.8+ (included with Blender)
- Internet connection for AI services

## Installation

1. Download the addon zip file
2. In Blender, go to Edit > Preferences > Add-ons
3. Click "Install..." and select the zip file
4. Enable "AI Agent System" in the addon list
5. The system will automatically install required dependencies

## Quick Start

1. Open Blender and navigate to the 3D Viewport
2. In the sidebar (N-panel), find the "AI Agents" tab
3. Click "Install Dependencies" if prompted
4. Start creating your first AI agent!

## Development Status

This addon is currently in active development:

- ✅ **Phase 1**: Foundation & Infrastructure (Complete)
- ✅ **Phase 2**: Crew AI Integration (Complete)
- ✅ **Phase 3**: MCP Integration (Complete)
- ✅ **Phase 4A**: Chat-First Interface (Complete)
- 🔄 **Phase 4B**: Advanced UI Components (Next)
- ⏳ **Phase 5**: Testing & Polish

## Current Features (v1.3.0)

### 💬 **Chat-First AI Interface**
- **Natural Language Control**: Talk to Blender in Turkish/English
- **Context-Aware Assistant**: Understands your current scene and selection
- **Smart Suggestions**: Intelligent recommendations based on your workflow
- **One-Click Actions**: Execute complex operations with simple commands

### 🤖 **AI Agent System**
- **7 Specialized Agent Types**: Modeler, Material Artist, Animator, Lighting Artist, Render Specialist, Scene Manager, General Assistant
- **Multi-Agent Crews**: Create teams of agents working together
- **Template System**: Pre-configured agents and crews for common tasks
- **Background Processing**: Non-blocking task execution with progress tracking

### 🛠️ **Blender Integration**
- **30+ Blender Tools**: Mesh manipulation, materials, lighting, animation, rendering
- **Scene Management**: Collection organization, cleanup, optimization
- **Real-time Monitoring**: Live status of agents, crews, and tasks
- **Native UI**: Integrated panels in Blender's 3D Viewport

### 🔌 **MCP Integration**
- **Server Management**: Connect to external MCP servers (filesystem, web, database)
- **Tool Registry**: Unified access to 50+ tools from multiple sources
- **Context Sharing**: Real-time Blender context available to external tools
- **Server Templates**: Pre-configured servers for common use cases

### 🎨 **User Interface**
- **Professional Icon System**: Complete Blender-native icon integration
- **Context-Aware UI**: Dynamic icons based on tool categories
- **Consistent Visual Language**: Unified design across all panels
- **Intuitive Navigation**: Clear visual hierarchy and status indicators

### ⚙️ **System Features**
- **Dependency Management**: Automatic installation of required packages
- **Configuration System**: Persistent settings and preferences
- **Logging System**: Comprehensive logging with file output
- **Error Handling**: Graceful error recovery and user feedback

## Support

- GitHub Issues: [Report bugs and request features](https://github.com/inkbytefo/blender-ai-agent/issues)
- Documentation: [User Guide](docs/user_guide.md)

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Author

Created by **inkbytefo**
