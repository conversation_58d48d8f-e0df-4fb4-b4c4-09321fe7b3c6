"""
Dependency Manager - Handles Python package installation and virtual environment management
"""

import subprocess
import sys
import os
from pathlib import Path
import logging
import importlib.util

logger = logging.getLogger(__name__)

class DependencyManager:
    """Manages Python dependencies and virtual environment for the addon"""
    
    def __init__(self):
        self.addon_dir = Path(__file__).parent.parent
        self.requirements_file = self.addon_dir / "requirements.txt"
        self.venv_dir = self.addon_dir / "venv"
        self.installed_packages = set()
        
    def check_dependencies(self):
        """Check if all required dependencies are installed"""
        if not self.requirements_file.exists():
            logger.warning("Requirements file not found")
            return False
            
        missing_packages = []
        
        with open(self.requirements_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#'):
                    # Extract package name (before >= or ==)
                    package_name = line.split('>=')[0].split('==')[0].strip()
                    
                    if not self._is_package_installed(package_name):
                        missing_packages.append(package_name)
        
        if missing_packages:
            logger.info(f"Missing packages: {missing_packages}")
            return False
        
        logger.info("All dependencies are installed")
        return True
    
    def install_dependencies(self, progress_callback=None):
        """Install all required dependencies"""
        try:
            if not self.requirements_file.exists():
                raise FileNotFoundError("Requirements file not found")
            
            logger.info("Starting dependency installation")
            
            # Get Python executable
            python_exe = sys.executable
            
            # Read requirements
            with open(self.requirements_file, 'r') as f:
                requirements = [
                    line.strip() for line in f 
                    if line.strip() and not line.startswith('#')
                ]
            
            total_packages = len(requirements)
            
            for i, package in enumerate(requirements):
                if progress_callback:
                    progress_callback(i, total_packages, f"Installing {package}")
                
                logger.info(f"Installing {package}")
                
                # Install package
                result = subprocess.run([
                    python_exe, "-m", "pip", "install", package
                ], capture_output=True, text=True)
                
                if result.returncode != 0:
                    logger.error(f"Failed to install {package}: {result.stderr}")
                    raise RuntimeError(f"Failed to install {package}")
                
                # Extract package name for tracking
                package_name = package.split('>=')[0].split('==')[0].strip()
                self.installed_packages.add(package_name)
            
            if progress_callback:
                progress_callback(total_packages, total_packages, "Installation complete")
            
            logger.info("All dependencies installed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Dependency installation failed: {e}")
            raise
    
    def _is_package_installed(self, package_name):
        """Check if a specific package is installed"""
        try:
            spec = importlib.util.find_spec(package_name)
            return spec is not None
        except (ImportError, ValueError, ModuleNotFoundError):
            return False
    
    def get_installed_packages(self):
        """Get list of installed packages"""
        try:
            result = subprocess.run([
                sys.executable, "-m", "pip", "list", "--format=freeze"
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                packages = []
                for line in result.stdout.split('\n'):
                    if line.strip() and '==' in line:
                        packages.append(line.strip())
                return packages
            else:
                logger.error(f"Failed to get package list: {result.stderr}")
                return []
                
        except Exception as e:
            logger.error(f"Error getting package list: {e}")
            return []
    
    def uninstall_dependencies(self):
        """Uninstall addon-specific dependencies (optional cleanup)"""
        try:
            if not self.requirements_file.exists():
                return True
            
            logger.info("Uninstalling addon dependencies")
            
            with open(self.requirements_file, 'r') as f:
                requirements = [
                    line.strip().split('>=')[0].split('==')[0].strip()
                    for line in f 
                    if line.strip() and not line.startswith('#')
                ]
            
            for package in requirements:
                try:
                    subprocess.run([
                        sys.executable, "-m", "pip", "uninstall", package, "-y"
                    ], capture_output=True)
                    logger.info(f"Uninstalled {package}")
                except Exception as e:
                    logger.warning(f"Failed to uninstall {package}: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to uninstall dependencies: {e}")
            return False

# Global dependency manager instance
_dependency_manager = DependencyManager()

def get_dependency_manager():
    """Get the dependency manager instance"""
    return _dependency_manager

def check_dependencies():
    """Check if dependencies are installed"""
    return _dependency_manager.check_dependencies()

def install_dependencies(progress_callback=None):
    """Install dependencies with optional progress callback"""
    return _dependency_manager.install_dependencies(progress_callback)
