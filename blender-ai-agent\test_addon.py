"""
Test script for AI Agent System addon
Run this script in Blender to test the addon functionality
"""

import bpy
import sys
from pathlib import Path

def test_addon_registration():
    """Test if the addon can be registered successfully"""
    print("Testing addon registration...")
    
    try:
        # Add addon path to sys.path if not already there
        addon_path = Path(__file__).parent
        if str(addon_path) not in sys.path:
            sys.path.insert(0, str(addon_path))
        
        # Import and register the addon
        import blender_ai_agent
        
        # Test registration
        blender_ai_agent.register()
        print("✓ Addon registered successfully")
        
        # Test if panels are available
        if hasattr(bpy.types, 'AI_AGENT_PT_main_panel'):
            print("✓ Main panel registered")
        else:
            print("✗ Main panel not found")
        
        # Test if operators are available
        operators_to_test = [
            'ai_agent.install_dependencies',
            'ai_agent.system_status',
            'ai_agent.open_preferences',
            'ai_agent.view_logs'
        ]
        
        for op_id in operators_to_test:
            if hasattr(bpy.ops, op_id.split('.')[0]) and hasattr(getattr(bpy.ops, op_id.split('.')[0]), op_id.split('.')[1]):
                print(f"✓ Operator {op_id} registered")
            else:
                print(f"✗ Operator {op_id} not found")
        
        # Test preferences
        if hasattr(bpy.types, 'AIAgentSettings'):
            print("✓ Settings class registered")
        else:
            print("✗ Settings class not found")
        
        # Test unregistration
        blender_ai_agent.unregister()
        print("✓ Addon unregistered successfully")
        
        return True
        
    except Exception as e:
        print(f"✗ Addon registration failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_core_modules():
    """Test core module functionality"""
    print("\nTesting core modules...")

    try:
        # Test addon manager
        from core.addon_manager import get_manager
        manager = get_manager()
        print("✓ Addon manager imported")

        # Test dependency manager
        from core.dependency_manager import get_dependency_manager
        dep_manager = get_dependency_manager()
        print("✓ Dependency manager imported")

        # Test config manager
        from core.config_manager import get_config_manager
        config_manager = get_config_manager()
        print("✓ Config manager imported")

        # Test config operations
        test_value = config_manager.get('system.log_level', 'INFO')
        print(f"✓ Config get operation: log_level = {test_value}")

        config_manager.set('test.value', 'test_data')
        retrieved_value = config_manager.get('test.value')
        if retrieved_value == 'test_data':
            print("✓ Config set/get operations working")
        else:
            print("✗ Config set/get operations failed")

        return True

    except Exception as e:
        print(f"✗ Core modules test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_crew_system():
    """Test crew system functionality"""
    print("\nTesting crew system...")

    try:
        # Test agent factory
        from crew_system.agent_factory import get_agent_factory
        agent_factory = get_agent_factory()
        print("✓ Agent factory imported")

        # Test templates
        templates = agent_factory.list_templates()
        print(f"✓ Agent templates loaded: {len(templates['default'])} default, {len(templates['custom'])} custom")

        # Test crew manager
        from crew_system.crew_manager import get_crew_manager
        crew_manager = get_crew_manager()
        print("✓ Crew manager imported")

        # Test task executor
        from crew_system.task_executor import get_task_executor
        task_executor = get_task_executor()
        print("✓ Task executor imported")

        # Test Blender tools
        from crew_system.blender_tools import get_blender_tools
        blender_tools = get_blender_tools()
        print("✓ Blender tools imported")

        # Test tool functionality
        scene_info = blender_tools.get_scene_info()
        print(f"✓ Scene info retrieved: {scene_info['objects_count']} objects")

        return True

    except Exception as e:
        print(f"✗ Crew system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chat_system():
    """Test chat system functionality"""
    print("\nTesting chat system...")

    try:
        # Test NLP processor
        from chat_system.nlp_processor import get_nlp_processor
        nlp_processor = get_nlp_processor()
        nlp_processor.initialize()
        print("✓ NLP processor initialized")

        # Test intent recognition
        test_message = "küp oluştur"
        parsed = nlp_processor.parse_message(test_message)
        print(f"✓ Intent recognition: {parsed.intent.value}")

        # Test response generation
        response = nlp_processor.generate_response(parsed)
        print(f"✓ Response generated: {response[:50]}...")

        # Test chat interface
        from chat_system.chat_interface import get_chat_interface
        chat_interface = get_chat_interface()
        chat_interface.initialize()
        print("✓ Chat interface initialized")

        # Test message handling
        msg_id = chat_interface.add_user_message("Test mesajı")
        print(f"✓ Message added: {msg_id}")

        # Test suggestions system
        from chat_system.suggestions import get_suggestions_system
        suggestions_system = get_suggestions_system()
        suggestions_system.initialize()
        print("✓ Suggestions system initialized")

        # Test suggestions
        suggestions = suggestions_system.get_suggestions(3)
        print(f"✓ Suggestions generated: {len(suggestions)} items")

        # Test agent bridge
        from chat_system.agent_bridge import get_agent_bridge
        agent_bridge = get_agent_bridge()
        agent_bridge.initialize()
        print("✓ Agent bridge initialized")

        return True

    except Exception as e:
        print(f"✗ Chat system test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mcp_integration():
    """Test MCP integration functionality"""
    print("\nTesting MCP integration...")

    try:
        # Test MCP client
        from mcp_integration.mcp_client import get_mcp_client
        mcp_client = get_mcp_client()
        print("✓ MCP client imported")

        # Test server manager
        from mcp_integration.server_manager import get_server_manager
        server_manager = get_server_manager()
        print("✓ Server manager imported")

        # Test templates
        templates = server_manager.list_templates()
        print(f"✓ Server templates loaded: {len(templates)}")

        # Test tool registry
        from mcp_integration.tool_registry import get_tool_registry
        tool_registry = get_tool_registry()
        print("✓ Tool registry imported")

        # Test tool statistics
        stats = tool_registry.get_tool_statistics()
        print(f"✓ Tool statistics: {stats['total_tools']} tools available")

        # Test context provider
        from mcp_integration.context_provider import get_context_provider
        context_provider = get_context_provider()
        print("✓ Context provider imported")

        # Test context summary
        summary = context_provider.get_context_summary()
        print(f"✓ Context summary retrieved: {summary.get('scene', {}).get('name', 'Unknown')} scene")

        return True

    except Exception as e:
        print(f"✗ MCP integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_logging():
    """Test logging functionality"""
    print("\nTesting logging...")
    
    try:
        from utils.logging_config import setup_logging, get_logger
        
        # Setup logging
        setup_logging()
        print("✓ Logging setup successful")
        
        # Test logger
        logger = get_logger("test")
        logger.info("Test log message")
        print("✓ Logger working")
        
        return True
        
    except Exception as e:
        print(f"✗ Logging test failed: {e}")
        return False

def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("AI Agent System - Addon Test Suite")
    print("=" * 50)
    
    tests = [
        ("Addon Registration", test_addon_registration),
        ("Core Modules", test_core_modules),
        ("Crew System", test_crew_system),
        ("Chat System", test_chat_system),
        ("MCP Integration", test_mcp_integration),
        ("Logging", test_logging)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        if test_func():
            passed += 1
            print(f"✓ {test_name} PASSED")
        else:
            print(f"✗ {test_name} FAILED")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    print("=" * 50)
    
    if passed == total:
        print("🎉 All tests passed! Addon is ready for use.")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    run_all_tests()
