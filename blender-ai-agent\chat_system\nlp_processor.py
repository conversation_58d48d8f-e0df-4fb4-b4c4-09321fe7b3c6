"""
Natural Language Processor - Turkish intent recognition and response generation
"""

import re
import json
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum

from ..utils.logging_config import get_logger

logger = get_logger("nlp_processor")

class Intent(Enum):
    """User intent categories"""
    CREATE_OBJECT = "create_object"
    MODIFY_OBJECT = "modify_object"
    ANIMATE = "animate"
    MATERIAL = "material"
    LIGHTING = "lighting"
    RENDER = "render"
    SCENE_MANAGEMENT = "scene_management"
    HELP = "help"
    UNKNOWN = "unknown"

@dataclass
class ParsedMessage:
    """Parsed user message with intent and parameters"""
    original_text: str
    intent: Intent
    confidence: float
    entities: Dict[str, Any]
    parameters: Dict[str, Any]
    context: Dict[str, Any]

class NLPProcessor:
    """Natural language processor for Turkish AI commands"""
    
    def __init__(self):
        self.intent_patterns = {}
        self.entity_patterns = {}
        self.response_templates = {}
        self.context_memory = {}
        self.is_initialized = False
        
    def initialize(self):
        """Initialize NLP processor with patterns and templates"""
        try:
            self._load_intent_patterns()
            self._load_entity_patterns()
            self._load_response_templates()
            self.is_initialized = True
            logger.info("NLP processor initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize NLP processor: {e}")
            raise
    
    def _load_intent_patterns(self):
        """Load Turkish intent recognition patterns"""
        self.intent_patterns = {
            Intent.CREATE_OBJECT: [
                # Creation verbs
                r'\b(oluştur|yap|üret|kur|inşa\s+et|yarat|ekle|çiz)\b',
                # Object types
                r'\b(obje|nesne|model|karakter|bina|ev|araba|masa|sandalye)\b',
                # Creation phrases
                r'\b(bir\s+.*\s+(yap|oluştur|üret))\b',
                r'\b(.*\s+için\s+.*\s+(yap|oluştur))\b'
            ],
            
            Intent.MODIFY_OBJECT: [
                # Modification verbs
                r'\b(değiştir|düzenle|ayarla|güncelle|revize\s+et|modifiye\s+et)\b',
                # Modification targets
                r'\b(boyut|ölçek|renk|şekil|form|yapı)\b',
                # Modification phrases
                r'\b(.*\s+(büyüt|küçült|genişlet|daralt))\b'
            ],
            
            Intent.ANIMATE: [
                # Animation verbs
                r'\b(animasyon|hareket|keyframe|canlandır|oynat)\b',
                # Animation types
                r'\b(yürü|koş|uç|dön|sallan|dans\s+et)\b',
                # Animation phrases
                r'\b(.*\s+(hareketli|animasyonlu)\s+yap)\b'
            ],
            
            Intent.MATERIAL: [
                # Material terms
                r'\b(materyal|tekstür|malzeme|yüzey|renk|doku)\b',
                # Material types
                r'\b(metal|ahşap|plastik|cam|kumaş|taş|beton)\b',
                # Material actions
                r'\b(boyama|renklendirme|kaplama)\b'
            ],
            
            Intent.LIGHTING: [
                # Lighting terms
                r'\b(ışık|aydınlatma|lamba|spot|güneş|gölge)\b',
                # Lighting actions
                r'\b(aydınlat|ışıklandır|parlaklık|karanlık)\b',
                # Lighting types
                r'\b(doğal|yapay|renkli|beyaz)\s+ışık\b'
            ],
            
            Intent.RENDER: [
                # Render terms
                r'\b(render|çıktı|görüntü|fotoğraf|video|animasyon\s+çıktısı)\b',
                # Render actions
                r'\b(çıkar|kaydet|export|dışa\s+aktar)\b',
                # Quality terms
                r'\b(yüksek\s+kalite|4k|hd|düşük\s+kalite)\b'
            ],
            
            Intent.SCENE_MANAGEMENT: [
                # Scene terms
                r'\b(sahne|ortam|dünya|çevre|arka\s+plan)\b',
                # Organization terms
                r'\b(organize|düzenle|temizle|grupla|koleksiyon)\b',
                # Scene actions
                r'\b(sil|kaldır|gizle|göster)\b'
            ],
            
            Intent.HELP: [
                # Help requests
                r'\b(yardım|nasıl|ne\s+yapmalı|öğren|anlat|göster)\b',
                # Question words
                r'\b(nedir|nereye|niçin|neden|kim|ne\s+zaman)\b'
            ]
        }
    
    def _load_entity_patterns(self):
        """Load entity extraction patterns"""
        self.entity_patterns = {
            'object_type': {
                'mesh_objects': r'\b(küp|küre|silindir|koni|düzlem|maymun|torus)\b',
                'characters': r'\b(karakter|insan|robot|yaratık|hayvan)\b',
                'buildings': r'\b(ev|bina|kule|köprü|duvar|çatı)\b',
                'vehicles': r'\b(araba|uçak|gemi|bisiklet|motor)\b',
                'furniture': r'\b(masa|sandalye|yatak|dolap|raf)\b'
            },
            
            'style': {
                'art_style': r'\b(gerçekçi|stilize|cartoon|anime|low\s*poly|high\s*poly)\b',
                'architectural': r'\b(modern|klasik|gotik|barok|minimalist)\b'
            },
            
            'material_type': {
                'metals': r'\b(çelik|demir|altın|gümüş|bakır|alüminyum)\b',
                'organics': r'\b(ahşap|deri|kumaş|kağıt|plastik)\b',
                'stones': r'\b(mermer|granit|taş|beton|tuğla)\b'
            },
            
            'colors': {
                'basic': r'\b(kırmızı|mavi|yeşil|sarı|mor|turuncu|pembe|siyah|beyaz|gri)\b',
                'advanced': r'\b(lacivert|bordo|turkuaz|lime|magenta)\b'
            },
            
            'numbers': {
                'quantity': r'\b(\d+|bir|iki|üç|dört|beş|altı|yedi|sekiz|dokuz|on)\b',
                'dimensions': r'\b(\d+(?:\.\d+)?)\s*(metre|cm|mm|birim)\b'
            }
        }
    
    def _load_response_templates(self):
        """Load response templates for different intents"""
        self.response_templates = {
            Intent.CREATE_OBJECT: [
                "Harika! {object_type} oluşturmak için gerekli crew'u kuruyorum.",
                "Mükemmel! {object_type} için {style} tarzında bir model yapayım.",
                "Anladım! {object_type} oluşturma işlemini başlatıyorum."
            ],
            
            Intent.MODIFY_OBJECT: [
                "Tabii! Seçili objeyi {modification} şeklinde değiştiriyorum.",
                "Hemen {target} özelliğini {modification} olarak ayarlıyorum.",
                "Değişiklik yapıyorum: {modification}"
            ],
            
            Intent.ANIMATE: [
                "Animasyon oluşturuyorum! {animation_type} hareketi ekleyeceğim.",
                "Harika! {object} için {animation_type} animasyonu yapıyorum.",
                "Animasyon crew'u kuruyor ve {animation_type} hareketi ekliyorum."
            ],
            
            Intent.MATERIAL: [
                "Materyal oluşturuyorum! {material_type} için PBR materyal hazırlıyorum.",
                "{color} renkli {material_type} materyal ekliyorum.",
                "Materyal crew'u {material_type} tekstürü oluşturuyor."
            ],
            
            Intent.LIGHTING: [
                "Aydınlatma ayarlıyorum! {lighting_type} ışık ekliyorum.",
                "Sahne için {lighting_setup} aydınlatma kuruyorum.",
                "Lighting crew'u sahneyi {lighting_type} ile aydınlatıyor."
            ],
            
            Intent.RENDER: [
                "Render ayarlarını {quality} kalite için yapılandırıyorum.",
                "Çıktı alıyorum! {format} formatında {quality} kalitede.",
                "Render crew'u {settings} ayarlarıyla çıktı hazırlıyor."
            ],
            
            Intent.SCENE_MANAGEMENT: [
                "Sahne düzenlemesi yapıyorum! {action} işlemini gerçekleştiriyorum.",
                "Organizasyon crew'u sahneyi {organization_type} şeklinde düzenliyor.",
                "Sahne yönetimi: {action}"
            ],
            
            Intent.HELP: [
                "Tabii ki yardım edebilirim! {topic} hakkında ne öğrenmek istiyorsun?",
                "Size {topic} konusunda rehberlik edebilirim.",
                "Yardım için buradayım! {topic} ile ilgili sorularınızı yanıtlayabilirim."
            ],
            
            Intent.UNKNOWN: [
                "Üzgünüm, tam olarak ne istediğinizi anlayamadım. Biraz daha detay verebilir misiniz?",
                "Bu konuda size nasıl yardım edebileceğimi anlayamadım. Lütfen farklı bir şekilde ifade edebilir misiniz?",
                "Anlamadığım bir kısım var. Daha açık bir şekilde söyleyebilir misiniz?"
            ]
        }
    
    def parse_message(self, message: str, context: Optional[Dict] = None) -> ParsedMessage:
        """Parse user message and extract intent and entities"""
        try:
            message = message.lower().strip()
            
            # Detect intent
            intent, confidence = self._detect_intent(message)
            
            # Extract entities
            entities = self._extract_entities(message, intent)
            
            # Extract parameters
            parameters = self._extract_parameters(message, intent, entities)
            
            # Merge with context
            merged_context = self.context_memory.copy()
            if context:
                merged_context.update(context)
            
            return ParsedMessage(
                original_text=message,
                intent=intent,
                confidence=confidence,
                entities=entities,
                parameters=parameters,
                context=merged_context
            )
            
        except Exception as e:
            logger.error(f"Failed to parse message: {e}")
            return ParsedMessage(
                original_text=message,
                intent=Intent.UNKNOWN,
                confidence=0.0,
                entities={},
                parameters={},
                context={}
            )
    
    def _detect_intent(self, message: str) -> Tuple[Intent, float]:
        """Detect user intent from message"""
        intent_scores = {}
        
        for intent, patterns in self.intent_patterns.items():
            score = 0
            matches = 0
            
            for pattern in patterns:
                if re.search(pattern, message, re.IGNORECASE):
                    matches += 1
                    score += 1
            
            if matches > 0:
                # Normalize score by number of patterns
                intent_scores[intent] = score / len(patterns)
        
        if not intent_scores:
            return Intent.UNKNOWN, 0.0
        
        # Get intent with highest score
        best_intent = max(intent_scores, key=intent_scores.get)
        confidence = intent_scores[best_intent]
        
        return best_intent, confidence
    
    def _extract_entities(self, message: str, intent: Intent) -> Dict[str, Any]:
        """Extract entities from message based on intent"""
        entities = {}
        
        for entity_type, patterns in self.entity_patterns.items():
            for pattern_name, pattern in patterns.items():
                matches = re.findall(pattern, message, re.IGNORECASE)
                if matches:
                    if entity_type not in entities:
                        entities[entity_type] = {}
                    entities[entity_type][pattern_name] = matches
        
        return entities
    
    def _extract_parameters(self, message: str, intent: Intent, entities: Dict) -> Dict[str, Any]:
        """Extract parameters for agent execution"""
        parameters = {}
        
        # Extract based on intent
        if intent == Intent.CREATE_OBJECT:
            parameters['action'] = 'create'
            if 'object_type' in entities:
                parameters['object_type'] = entities['object_type']
            if 'style' in entities:
                parameters['style'] = entities['style']
        
        elif intent == Intent.MODIFY_OBJECT:
            parameters['action'] = 'modify'
            if 'numbers' in entities:
                parameters['values'] = entities['numbers']
        
        elif intent == Intent.ANIMATE:
            parameters['action'] = 'animate'
            # Extract animation type from context
        
        elif intent == Intent.MATERIAL:
            parameters['action'] = 'material'
            if 'material_type' in entities:
                parameters['material_type'] = entities['material_type']
            if 'colors' in entities:
                parameters['color'] = entities['colors']
        
        return parameters
    
    def generate_response(self, parsed_message: ParsedMessage) -> str:
        """Generate appropriate response for parsed message"""
        try:
            templates = self.response_templates.get(parsed_message.intent, 
                                                  self.response_templates[Intent.UNKNOWN])
            
            # Select template (for now, use first one)
            template = templates[0]
            
            # Fill template with entities and parameters
            response_data = {}
            if parsed_message.entities:
                response_data.update(parsed_message.entities)
            if parsed_message.parameters:
                response_data.update(parsed_message.parameters)
            
            # Flatten nested dictionaries for template formatting
            flat_data = {}
            for key, value in response_data.items():
                if isinstance(value, dict):
                    for subkey, subvalue in value.items():
                        if isinstance(subvalue, list) and subvalue:
                            flat_data[f"{key}_{subkey}"] = subvalue[0]
                        else:
                            flat_data[f"{key}_{subkey}"] = subvalue
                else:
                    flat_data[key] = value
            
            # Format template safely
            try:
                response = template.format(**flat_data)
            except KeyError:
                # If template formatting fails, use a generic response
                response = template.split('{')[0].strip()
                if not response:
                    response = "Anladım! İsteğinizi işleme alıyorum."
            
            return response
            
        except Exception as e:
            logger.error(f"Failed to generate response: {e}")
            return "Üzgünüm, bir hata oluştu. Lütfen tekrar deneyin."
    
    def update_context(self, key: str, value: Any):
        """Update conversation context"""
        self.context_memory[key] = value
    
    def get_context(self, key: str) -> Any:
        """Get value from conversation context"""
        return self.context_memory.get(key)
    
    def clear_context(self):
        """Clear conversation context"""
        self.context_memory.clear()
    
    def cleanup(self):
        """Cleanup NLP processor"""
        self.clear_context()
        self.is_initialized = False
        logger.info("NLP processor cleaned up")

# Global NLP processor instance
_nlp_processor = None

def get_nlp_processor():
    """Get the NLP processor instance"""
    global _nlp_processor
    if _nlp_processor is None:
        _nlp_processor = NLPProcessor()
    return _nlp_processor
