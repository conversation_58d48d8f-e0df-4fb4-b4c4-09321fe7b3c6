"""
Agent Bridge - Convert natural language to agent commands and responses
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

from .nlp_processor import get_nlp_processor, ParsedMessage, Intent
from .llm_nlp_processor import LLMNLPProcessor
from .chat_interface import get_chat_interface, MessageType
from ..crew_system.crew_manager import get_crew_manager
from ..crew_system.agent_factory import get_agent_factory
from ..crew_system.blender_tools import get_blender_tools
from ..mcp_integration.context_provider import get_context_provider
from ..config.llm_config import get_llm_config_manager
from ..utils.logging_config import get_logger

logger = get_logger("agent_bridge")

class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class AgentTask:
    """Agent task representation"""
    id: str
    intent: Intent
    description: str
    parameters: Dict[str, Any]
    status: TaskStatus
    result: Optional[Any] = None
    error: Optional[str] = None
    progress: float = 0.0

class AgentBridge:
    """Bridge between natural language and agent system"""
    
    def __init__(self):
        self.nlp_processor = get_nlp_processor()  # Legacy processor
        self.llm_nlp_processor: Optional[LLMNLPProcessor] = None  # New LLM processor
        self.chat_interface = get_chat_interface()
        self.crew_manager = get_crew_manager()
        self.agent_factory = get_agent_factory()
        self.blender_tools = get_blender_tools()
        self.context_provider = get_context_provider()
        self.config_manager = get_llm_config_manager()

        self.active_tasks: Dict[str, AgentTask] = {}
        self.task_counter = 0
        self.is_initialized = False
        self.use_llm_processor = True  # Flag to switch between processors

        # Callbacks
        self.on_task_started: Optional[Callable] = None
        self.on_task_completed: Optional[Callable] = None
        self.on_task_failed: Optional[Callable] = None
    
    async def initialize(self):
        """Initialize agent bridge"""
        try:
            # Initialize LLM processor if config is available
            llm_config = self.config_manager.get_current_config()
            if llm_config and self.use_llm_processor:
                self.llm_nlp_processor = LLMNLPProcessor(llm_config)
                if await self.llm_nlp_processor.initialize():
                    logger.info("LLM NLP processor initialized successfully")
                else:
                    logger.warning("Failed to initialize LLM processor, falling back to regex processor")
                    self.use_llm_processor = False
            else:
                logger.info("Using legacy regex-based NLP processor")
                self.use_llm_processor = False

            # Set up chat interface callbacks
            self.chat_interface.on_message_sent = self._handle_user_message

            self.is_initialized = True
            logger.info("Agent bridge initialized")

        except Exception as e:
            logger.error(f"Failed to initialize agent bridge: {e}")
            raise
    
    def _handle_user_message(self, message):
        """Handle incoming user message"""
        try:
            # Start typing indicator
            self.chat_interface.start_typing()

            # Get current Blender context
            context = self._get_blender_context()

            # Parse message with appropriate processor
            if self.use_llm_processor and self.llm_nlp_processor:
                # Use async LLM processor
                import asyncio
                parsed = asyncio.run(self.llm_nlp_processor.parse_message(message.content, context))
                response = asyncio.run(self.llm_nlp_processor.generate_response(parsed))
            else:
                # Use legacy regex processor
                parsed = self.nlp_processor.parse_message(message.content, context)
                response = self.nlp_processor.generate_response(parsed)

            # Add assistant response
            self.chat_interface.stop_typing()
            self.chat_interface.add_assistant_message(response)

            # Execute agent task if needed
            if parsed.intent != Intent.UNKNOWN and parsed.intent != Intent.HELP:
                self._execute_agent_task(parsed)

        except Exception as e:
            self.chat_interface.stop_typing()
            self.chat_interface.add_error_message(f"Üzgünüm, bir hata oluştu: {e}")
            logger.error(f"Failed to handle user message: {e}")
    
    def _get_blender_context(self) -> Dict[str, Any]:
        """Get current Blender context for NLP"""
        try:
            context_summary = self.context_provider.get_context_summary()
            
            return {
                'scene_name': context_summary.get('scene', {}).get('name', 'Unknown'),
                'selected_objects': context_summary.get('selection', {}).get('selected_objects', []),
                'active_object': context_summary.get('selection', {}).get('active_object'),
                'mode': context_summary.get('mode', 'OBJECT'),
                'total_objects': context_summary.get('statistics', {}).get('total_objects', 0)
            }
            
        except Exception as e:
            logger.warning(f"Failed to get Blender context: {e}")
            return {}
    
    def _execute_agent_task(self, parsed_message: ParsedMessage):
        """Execute agent task based on parsed message"""
        try:
            # Create task
            task_id = f"task_{self.task_counter}"
            self.task_counter += 1
            
            task = AgentTask(
                id=task_id,
                intent=parsed_message.intent,
                description=parsed_message.original_text,
                parameters=parsed_message.parameters,
                status=TaskStatus.PENDING
            )
            
            self.active_tasks[task_id] = task
            
            # Execute task based on intent
            if parsed_message.intent == Intent.CREATE_OBJECT:
                self._handle_create_object(task, parsed_message)
            elif parsed_message.intent == Intent.MODIFY_OBJECT:
                self._handle_modify_object(task, parsed_message)
            elif parsed_message.intent == Intent.ANIMATE:
                self._handle_animate(task, parsed_message)
            elif parsed_message.intent == Intent.MATERIAL:
                self._handle_material(task, parsed_message)
            elif parsed_message.intent == Intent.LIGHTING:
                self._handle_lighting(task, parsed_message)
            elif parsed_message.intent == Intent.RENDER:
                self._handle_render(task, parsed_message)
            elif parsed_message.intent == Intent.SCENE_MANAGEMENT:
                self._handle_scene_management(task, parsed_message)
            
        except Exception as e:
            logger.error(f"Failed to execute agent task: {e}")
            self.chat_interface.add_error_message(f"Görev çalıştırılamadı: {e}")
    
    def _handle_create_object(self, task: AgentTask, parsed: ParsedMessage):
        """Handle object creation task"""
        try:
            task.status = TaskStatus.RUNNING
            
            # Extract object type from entities
            object_type = "cube"  # default
            if parsed.entities and 'object_type' in parsed.entities:
                obj_types = parsed.entities['object_type']
                if 'mesh_objects' in obj_types and obj_types['mesh_objects']:
                    object_type = obj_types['mesh_objects'][0]
                elif 'characters' in obj_types and obj_types['characters']:
                    object_type = obj_types['characters'][0]
                elif 'buildings' in obj_types and obj_types['buildings']:
                    object_type = obj_types['buildings'][0]
            
            # Use Blender tools to create object
            if object_type in ['küp', 'cube']:
                result = self.blender_tools.create_primitive_mesh('cube')
            elif object_type in ['küre', 'sphere']:
                result = self.blender_tools.create_primitive_mesh('sphere')
            elif object_type in ['silindir', 'cylinder']:
                result = self.blender_tools.create_primitive_mesh('cylinder')
            else:
                # Default to cube
                result = self.blender_tools.create_primitive_mesh('cube')
            
            if result.success:
                task.status = TaskStatus.COMPLETED
                task.result = result.data
                self.chat_interface.add_assistant_message(
                    f"✅ {object_type.title()} başarıyla oluşturuldu!"
                )
            else:
                task.status = TaskStatus.FAILED
                task.error = result.error
                self.chat_interface.add_error_message(
                    f"❌ {object_type.title()} oluşturulamadı: {result.error}"
                )
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            logger.error(f"Failed to create object: {e}")
    
    def _handle_modify_object(self, task: AgentTask, parsed: ParsedMessage):
        """Handle object modification task"""
        try:
            task.status = TaskStatus.RUNNING
            
            # Get selected object
            context = self._get_blender_context()
            active_object = context.get('active_object')
            
            if not active_object:
                task.status = TaskStatus.FAILED
                task.error = "No object selected"
                self.chat_interface.add_error_message(
                    "❌ Lütfen önce bir obje seçin."
                )
                return
            
            # For now, just acknowledge the modification
            task.status = TaskStatus.COMPLETED
            self.chat_interface.add_assistant_message(
                f"✅ {active_object} objesi için değişiklik planlandı."
            )
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            logger.error(f"Failed to modify object: {e}")
    
    def _handle_animate(self, task: AgentTask, parsed: ParsedMessage):
        """Handle animation task"""
        try:
            task.status = TaskStatus.RUNNING
            
            # Get selected object
            context = self._get_blender_context()
            active_object = context.get('active_object')
            
            if not active_object:
                task.status = TaskStatus.FAILED
                self.chat_interface.add_error_message(
                    "❌ Animasyon için lütfen bir obje seçin."
                )
                return
            
            # For now, acknowledge animation request
            task.status = TaskStatus.COMPLETED
            self.chat_interface.add_assistant_message(
                f"✅ {active_object} için animasyon planlandı."
            )
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            logger.error(f"Failed to animate: {e}")
    
    def _handle_material(self, task: AgentTask, parsed: ParsedMessage):
        """Handle material creation task"""
        try:
            task.status = TaskStatus.RUNNING
            
            # Extract material info
            material_name = "AI_Material"
            material_type = "basic"
            
            if parsed.entities and 'material_type' in parsed.entities:
                mat_types = parsed.entities['material_type']
                if 'metals' in mat_types and mat_types['metals']:
                    material_type = mat_types['metals'][0]
                    material_name = f"AI_{material_type.title()}"
            
            # Create material using Blender tools
            result = self.blender_tools.create_material(material_name)
            
            if result.success:
                task.status = TaskStatus.COMPLETED
                self.chat_interface.add_assistant_message(
                    f"✅ {material_name} materyali oluşturuldu!"
                )
            else:
                task.status = TaskStatus.FAILED
                self.chat_interface.add_error_message(
                    f"❌ Materyal oluşturulamadı: {result.error}"
                )
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            logger.error(f"Failed to create material: {e}")
    
    def _handle_lighting(self, task: AgentTask, parsed: ParsedMessage):
        """Handle lighting task"""
        try:
            task.status = TaskStatus.RUNNING
            
            # Add basic lighting
            result = self.blender_tools.add_light("SUN")
            
            if result.success:
                task.status = TaskStatus.COMPLETED
                self.chat_interface.add_assistant_message(
                    "✅ Sahneye güneş ışığı eklendi!"
                )
            else:
                task.status = TaskStatus.FAILED
                self.chat_interface.add_error_message(
                    f"❌ Işık eklenemedi: {result.error}"
                )
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            logger.error(f"Failed to add lighting: {e}")
    
    def _handle_render(self, task: AgentTask, parsed: ParsedMessage):
        """Handle render task"""
        try:
            task.status = TaskStatus.RUNNING
            
            # Setup basic render settings
            result = self.blender_tools.setup_render_settings()
            
            if result.success:
                task.status = TaskStatus.COMPLETED
                self.chat_interface.add_assistant_message(
                    "✅ Render ayarları yapılandırıldı!"
                )
            else:
                task.status = TaskStatus.FAILED
                self.chat_interface.add_error_message(
                    f"❌ Render ayarları yapılandırılamadı: {result.error}"
                )
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            logger.error(f"Failed to setup render: {e}")
    
    def _handle_scene_management(self, task: AgentTask, parsed: ParsedMessage):
        """Handle scene management task"""
        try:
            task.status = TaskStatus.RUNNING
            
            # Get scene info
            scene_info = self.blender_tools.get_scene_info()
            
            if scene_info.success:
                task.status = TaskStatus.COMPLETED
                objects_count = scene_info.data.get('objects_count', 0)
                self.chat_interface.add_assistant_message(
                    f"✅ Sahne bilgisi: {objects_count} obje mevcut."
                )
            else:
                task.status = TaskStatus.FAILED
                self.chat_interface.add_error_message(
                    "❌ Sahne bilgisi alınamadı."
                )
            
        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            logger.error(f"Failed scene management: {e}")
    
    def get_active_tasks(self) -> List[AgentTask]:
        """Get list of active tasks"""
        return [task for task in self.active_tasks.values() 
                if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]]
    
    def cancel_task(self, task_id: str) -> bool:
        """Cancel a running task"""
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                task.status = TaskStatus.CANCELLED
                return True
        return False
    
    async def cleanup(self):
        """Cleanup agent bridge"""
        # Cancel all active tasks
        for task in self.active_tasks.values():
            if task.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                task.status = TaskStatus.CANCELLED

        # Cleanup LLM processor
        if self.llm_nlp_processor:
            await self.llm_nlp_processor.cleanup()
            self.llm_nlp_processor = None

        self.active_tasks.clear()
        self.is_initialized = False
        logger.info("Agent bridge cleaned up")

# Global agent bridge instance
_agent_bridge = None

def get_agent_bridge():
    """Get the agent bridge instance"""
    global _agent_bridge
    if _agent_bridge is None:
        _agent_bridge = AgentBridge()
    return _agent_bridge
