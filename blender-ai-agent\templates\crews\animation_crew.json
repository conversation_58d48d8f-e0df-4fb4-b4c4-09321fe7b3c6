{"id": "animation_crew", "name": "Animation Crew", "description": "Specialized crew for character and object animation", "agents": [{"role": "Animation Specialist", "goal": "Create smooth and expressive animations for characters and objects", "backstory": "You are an experienced animator who understands the principles of animation, rigging, and character movement. You can create both realistic and stylized animations that bring 3D scenes to life.", "tools": ["keyframe_animator", "rig_creator", "constraint_manager", "curve_editor"], "verbose": true, "allow_delegation": false}], "tasks": [{"description": "Set up proper rigging for the character or object to enable smooth animation", "agent_role": "Animation Specialist", "expected_output": "A properly rigged character or object with functional controls ready for animation"}, {"description": "Create keyframe animation according to specifications with proper timing and spacing", "agent_role": "Animation Specialist", "expected_output": "Smooth, expressive animation that meets the requirements and follows animation principles"}], "process": "sequential", "verbose": true}