"""
System Operators - Core system operations for AI Agent System
"""

import bpy
from bpy.types import Operator
from bpy.props import StringProperty
import subprocess
from pathlib import Path
from ..core.addon_manager import get_manager
from ..core.dependency_manager import get_dependency_manager
from ..core.config_manager import get_config_manager
from ..utils.logging_config import get_logger

logger = get_logger("system_operators")

class AI_AGENT_OT_install_dependencies(Operator):
    """Install required dependencies for AI Agent System"""
    bl_idname = "ai_agent.install_dependencies"
    bl_label = "Install Dependencies"
    bl_description = "Install required Python packages for AI Agent System"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            self.report({'INFO'}, "Installing dependencies...")
            logger.info("Starting dependency installation")

            dependency_manager = get_dependency_manager()

            # Progress callback for user feedback
            def progress_callback(current, total, message):
                progress = int((current / total) * 100) if total > 0 else 0
                self.report({'INFO'}, f"{message} ({progress}%)")
                logger.info(f"Progress: {progress}% - {message}")

            # Install dependencies using dependency manager
            success = dependency_manager.install_dependencies(progress_callback)

            if success:
                self.report({'INFO'}, "Dependencies installation completed successfully")
                logger.info("Dependencies installation completed successfully")

                # Update addon manager status
                addon_manager = get_manager()
                addon_manager._check_dependencies()
            else:
                self.report({'ERROR'}, "Dependencies installation failed")
                logger.error("Dependencies installation failed")

        except Exception as e:
            error_msg = f"Failed to install dependencies: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)

        return {'FINISHED'}

class AI_AGENT_OT_system_status(Operator):
    """Check system status"""
    bl_idname = "ai_agent.system_status"
    bl_label = "System Status"
    bl_description = "Check AI Agent System status and display information"
    bl_options = {'REGISTER'}

    def execute(self, context):
        addon_manager = get_manager()

        status_info = []
        status_info.append(f"System Initialized: {addon_manager.is_initialized}")
        status_info.append(f"Dependencies Available: {addon_manager.dependencies_installed}")
        status_info.append(f"Blender Version: {bpy.app.version}")

        status_message = " | ".join(status_info)
        self.report({'INFO'}, status_message)
        logger.info(f"System status checked: {status_message}")

        return {'FINISHED'}

class AI_AGENT_OT_open_preferences(Operator):
    """Open AI Agent System preferences"""
    bl_idname = "ai_agent.open_preferences"
    bl_label = "Open Preferences"
    bl_description = "Open AI Agent System preferences panel"
    bl_options = {'REGISTER'}

    def execute(self, context):
        # Open addon preferences
        bpy.ops.screen.userpref_show('INVOKE_DEFAULT')
        bpy.context.preferences.active_section = 'ADDONS'

        self.report({'INFO'}, "Opening preferences...")

        return {'FINISHED'}

class AI_AGENT_OT_view_logs(Operator):
    """View system logs"""
    bl_idname = "ai_agent.view_logs"
    bl_label = "View Logs"
    bl_description = "Open log directory in file explorer"
    bl_options = {'REGISTER'}

    def execute(self, context):
        addon_dir = Path(__file__).parent.parent
        logs_dir = addon_dir / "logs"

        if logs_dir.exists():
            # Open logs directory in file explorer
            import os
            import platform

            try:
                if platform.system() == "Windows":
                    os.startfile(logs_dir)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", logs_dir])
                else:  # Linux
                    subprocess.run(["xdg-open", logs_dir])

                self.report({'INFO'}, f"Opened logs directory: {logs_dir}")

            except Exception as e:
                self.report({'ERROR'}, f"Failed to open logs directory: {e}")
        else:
            self.report({'WARNING'}, "Logs directory not found")

        return {'FINISHED'}

# Placeholder operators for UI
class AI_AGENT_OT_create_agent(Operator):
    """Create new agent (placeholder)"""
    bl_idname = "ai_agent.create_agent"
    bl_label = "Create Agent"
    bl_description = "Create a new AI agent"
    bl_options = {'REGISTER'}

    def execute(self, context):
        self.report({'INFO'}, "Agent creation will be available in Phase 2")
        return {'FINISHED'}

class AI_AGENT_OT_manage_crews(Operator):
    """Manage crews (placeholder)"""
    bl_idname = "ai_agent.manage_crews"
    bl_label = "Manage Crews"
    bl_description = "Manage AI agent crews"
    bl_options = {'REGISTER'}

    def execute(self, context):
        self.report({'INFO'}, "Crew management will be available in Phase 2")
        return {'FINISHED'}

class AI_AGENT_OT_mcp_servers(Operator):
    """Manage MCP servers (placeholder)"""
    bl_idname = "ai_agent.mcp_servers"
    bl_label = "MCP Servers"
    bl_description = "Manage Model Context Protocol servers"
    bl_options = {'REGISTER'}

    def execute(self, context):
        self.report({'INFO'}, "MCP server management will be available in Phase 3")
        return {'FINISHED'}

# Configuration management operators
class AI_AGENT_OT_reset_config(Operator):
    """Reset configuration to defaults"""
    bl_idname = "ai_agent.reset_config"
    bl_label = "Reset Configuration"
    bl_description = "Reset all settings to default values"
    bl_options = {'REGISTER'}

    def execute(self, context):
        try:
            config_manager = get_config_manager()
            success = config_manager.reset_to_defaults()

            if success:
                self.report({'INFO'}, "Configuration reset to defaults")
                logger.info("Configuration reset to defaults")
            else:
                self.report({'ERROR'}, "Failed to reset configuration")
                logger.error("Failed to reset configuration")

        except Exception as e:
            error_msg = f"Error resetting configuration: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)

        return {'FINISHED'}

class AI_AGENT_OT_export_config(Operator):
    """Export configuration to file"""
    bl_idname = "ai_agent.export_config"
    bl_label = "Export Configuration"
    bl_description = "Export current configuration to a file"
    bl_options = {'REGISTER'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Path to export configuration",
        default="ai_agent_config.json",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            config_manager = get_config_manager()
            export_path = Path(self.filepath)

            success = config_manager.export_config(export_path)

            if success:
                self.report({'INFO'}, f"Configuration exported to {export_path}")
                logger.info(f"Configuration exported to {export_path}")
            else:
                self.report({'ERROR'}, "Failed to export configuration")
                logger.error("Failed to export configuration")

        except Exception as e:
            error_msg = f"Error exporting configuration: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)

        return {'FINISHED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

class AI_AGENT_OT_import_config(Operator):
    """Import configuration from file"""
    bl_idname = "ai_agent.import_config"
    bl_label = "Import Configuration"
    bl_description = "Import configuration from a file"
    bl_options = {'REGISTER'}

    filepath: bpy.props.StringProperty(
        name="File Path",
        description="Path to import configuration from",
        subtype='FILE_PATH'
    )

    def execute(self, context):
        try:
            config_manager = get_config_manager()
            import_path = Path(self.filepath)

            if not import_path.exists():
                self.report({'ERROR'}, "Configuration file not found")
                return {'CANCELLED'}

            success = config_manager.import_config(import_path)

            if success:
                self.report({'INFO'}, f"Configuration imported from {import_path}")
                logger.info(f"Configuration imported from {import_path}")
            else:
                self.report({'ERROR'}, "Failed to import configuration")
                logger.error("Failed to import configuration")

        except Exception as e:
            error_msg = f"Error importing configuration: {e}"
            self.report({'ERROR'}, error_msg)
            logger.error(error_msg)

        return {'FINISHED'}

    def invoke(self, context, event):
        context.window_manager.fileselect_add(self)
        return {'RUNNING_MODAL'}

# Classes to register
classes = [
    AI_AGENT_OT_install_dependencies,
    AI_AGENT_OT_system_status,
    AI_AGENT_OT_open_preferences,
    AI_AGENT_OT_view_logs,
    AI_AGENT_OT_create_agent,
    AI_AGENT_OT_manage_crews,
    AI_AGENT_OT_mcp_servers,
    AI_AGENT_OT_reset_config,
    AI_AGENT_OT_export_config,
    AI_AGENT_OT_import_config,
]

def register():
    """Register operator classes"""
    for cls in classes:
        bpy.utils.register_class(cls)

def unregister():
    """Unregister operator classes"""
    for cls in reversed(classes):
        bpy.utils.unregister_class(cls)
